document.addEventListener('DOMContentLoaded', function() {
  // Initialize global variables
  const isMobile = window.innerWidth < 768;
  const isMedium = window.innerWidth < 992;
  let currentPage = window.location.pathname.split('/').pop() || 'index.html';
  let audioEnabled = false;
  let animationsInitialized = false;
  
  // DOM elements
  const body = document.body;
  const preloader = document.getElementById('preloader');
  const backgroundAudio = document.getElementById('background-sound');
  const hoverSound = document.getElementById('hover-sound');
  const clickSound = document.getElementById('click-sound');
  const themeToggle = document.querySelector('.theme-toggle');
  const navToggle = document.querySelector('.nav-toggle');
  const nav = document.querySelector('nav');
  const dropdowns = document.querySelectorAll('.dropdown');
  const showreelButton = document.querySelector('.play-showreel');
  const showreelModal = document.getElementById('showreel-modal');
  const backToTopButton = document.querySelector('.back-to-top');
  const soundToggle = document.querySelector('.sound-toggle');
  const cursorDot = document.querySelector('.cursor-dot');
  const cursorOutline = document.querySelector('.cursor-outline');
  const contactForm = document.getElementById('contact-form');
  
  /* =======================
     CLIENT-SIDE ROUTING
  ======================= */
  const routes = {
      '/': 'index.html',
      '/services': 'services.html',
      '/about': 'about.html',
      '/contact': 'contact.html'
  };

  // Function to load content dynamically
  async function loadPage(path) {
      const file = routes[path] || 'index.html'; // Default to index.html if path not found
      try {
          const response = await fetch(file);
          if (!response.ok) {
              throw new Error(`Failed to fetch ${file}: ${response.status} ${response.statusText}`);
          }
          const html = await response.text();
          
          // Extract the content inside <main> tag
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');
          const newMainContent = doc.querySelector('main');
          if (!newMainContent) {
              throw new Error(`No <main> tag found in ${file}`);
          }

          // Update the current page's <main> content
          document.querySelector('main').innerHTML = newMainContent.innerHTML;

          // Update the active navigation link
          updateActiveLink(path);

          // Reinitialize scripts and event listeners
          reinitializeScripts();

          // Handle hash in URL (e.g., /services#generative-ai)
          if (window.location.hash) {
              const element = document.querySelector(window.location.hash);
              if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
              }
          }

          // Update currentPage variable
          currentPage = path === '/' ? 'index.html' : path.split('/').pop() + '.html';
      } catch (error) {
          console.error('Error loading page:', error);
          document.querySelector('main').innerHTML = '<h1>404 - Page Not Found</h1><p>Sorry, the requested page could not be loaded. Returning to Home...</p>';
          setTimeout(() => loadPage('/'), 2000);
      }
  }

  // Function to update the active navigation link
  function updateActiveLink(path) {
      const navLinks = document.querySelectorAll('.nav-link');
      navLinks.forEach(link => {
          link.classList.remove('active');
          if (link.getAttribute('href') === path) {
              link.classList.add('active');
          }
      });
  }

  // Function to reinitialize scripts and event listeners after content load
  function reinitializeScripts() {
    // Reinitialize testimonial slider
    initTestimonialSlider();
    // Reinitialize form validation
    initFormValidation();
    // Reinitialize smooth scrolling for hash links
    initSmoothScrolling();
    // Reinitialize GSAP animations
    initAnimations();
    // Reattach sound effects to interactive elements
    initSoundEffects();
    // Reattach showreel modal event listeners
    initShowreelModal();
    // Initialize FAQ accordion
    initFAQAccordion(); // Add this line
  }

  // Handle navigation link clicks
  document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', (e) => {
          e.preventDefault(); // Prevent default link behavior
          const path = link.getAttribute('href');
          
          // Update the URL without reloading
          history.pushState(null, '', path);

          // Load the corresponding page
          loadPage(path);

          // Play click sound
          if (audioEnabled && clickSound) {
              clickSound.currentTime = 0;
              clickSound.play();
          }

          // Close mobile menu if open
          if (nav.classList.contains('active')) {
              navToggle.classList.remove('active');
              nav.classList.remove('active');
              body.style.overflow = '';
          }
      });
  });

  // Handle browser back/forward buttons
  window.addEventListener('popstate', () => {
      const path = window.location.pathname;
      loadPage(path);
  });

  /* =======================
     PRELOADER
  ======================= */
  // Load the initial page immediately, before preloader finishes
  const initialPath = window.location.pathname;
  loadPage(initialPath); // Load the correct page content right away

  setTimeout(() => {
    preloader.style.opacity = '0';
    setTimeout(() => {
      preloader.style.display = 'none';
      
      // Initialize all animations after preloader
      if (!animationsInitialized) {
        initThreeJS();
        initAnimations();
        initParticles();
        animationsInitialized = true;
      }
    }, 500);
  }, 1500);
  
  /* =======================
     CUSTOM CURSOR
  ======================= */
  if (!isMobile) {
    // Show custom cursor on non-mobile devices
    setTimeout(() => {
      cursorDot.style.opacity = '1';
      cursorOutline.style.opacity = '1';
    }, 1000);
    
    window.addEventListener('mousemove', (e) => {
      cursorDot.style.left = `${e.clientX}px`;
      cursorDot.style.top = `${e.clientY}px`;
      
      // Add slight delay for cursor outline for smoother effect
      setTimeout(() => {
        cursorOutline.style.left = `${e.clientX}px`;
        cursorOutline.style.top = `${e.clientY}px`;
      }, 50);
    });
    
    // Cursor interactions
    const cursorElements = document.querySelectorAll('a, button, .service-card, .nav-toggle, .theme-toggle, .sound-toggle');
    
    cursorElements.forEach(element => {
      element.addEventListener('mouseenter', () => {
        cursorOutline.style.width = '50px';
        cursorOutline.style.height = '50px';
        cursorOutline.style.borderColor = 'var(--accent-color)';
      });
      
      element.addEventListener('mouseleave', () => {
        cursorOutline.style.width = '40px';
        cursorOutline.style.height = '40px';
        cursorOutline.style.borderColor = 'rgba(65, 105, 225, 0.5)';
      });
    });
  }
  
  /* =======================
     THEME TOGGLE
  ======================= */
  themeToggle.addEventListener('click', () => {
    if (body.getAttribute('data-theme') === 'light') {
      body.removeAttribute('data-theme');
      localStorage.setItem('theme', 'dark');
    } else {
      body.setAttribute('data-theme', 'light');
      localStorage.setItem('theme', 'light');
    }
    
    // Play click sound
    if (audioEnabled) {
      clickSound.currentTime = 0;
      clickSound.play();
    }
  });
  
  // Check saved theme preference
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme === 'light') {
    body.setAttribute('data-theme', 'light');
  }
  
  /* =======================
     MOBILE NAVIGATION
  ======================= */
  navToggle.addEventListener('click', () => {
    navToggle.classList.toggle('active');
    nav.classList.toggle('active');
    
    // Prevent scrolling when nav is open
    if (nav.classList.contains('active')) {
      body.style.overflow = 'hidden';
    } else {
      body.style.overflow = '';
    }
    
    // Play click sound
    if (audioEnabled) {
      clickSound.currentTime = 0;
      clickSound.play();
    }
  });
  
  // Handle dropdown on mobile
  dropdowns.forEach(dropdown => {
    const link = dropdown.querySelector('.nav-link');
    
    link.addEventListener('click', (e) => {
      if (isMobile) {
        e.preventDefault();
        dropdown.classList.toggle('active');
      }
    });
  });
  
  /* =======================
     SOUND CONTROLS
  ======================= */
  soundToggle.addEventListener('click', () => {
    const soundOn = soundToggle.querySelector('.sound-on');
    const soundOff = soundToggle.querySelector('.sound-off');
    
    if (audioEnabled) {
      // Turn off audio
      audioEnabled = false;
      soundOn.classList.remove('active');
      soundOff.classList.add('active');
      
      if (backgroundAudio) {
        backgroundAudio.pause();
      }
    } else {
      // Turn on audio
      audioEnabled = true;
      soundOn.classList.add('active');
      soundOff.classList.remove('active');
      
      if (backgroundAudio) {
        backgroundAudio.volume = 0.05;
        backgroundAudio.play().catch(error => console.log('Background audio failed:', error));
      }
    }
    
    localStorage.setItem('audioEnabled', audioEnabled);
    
    // Play click sound
    if (audioEnabled) {
      clickSound.currentTime = 0;
      clickSound.play();
    }
  });
  
  // Check saved audio preference
  const savedAudioEnabled = localStorage.getItem('audioEnabled');
  if (savedAudioEnabled === 'true') {
    const soundOn = soundToggle.querySelector('.sound-on');
    const soundOff = soundToggle.querySelector('.sound-off');
    
    audioEnabled = true;
    soundOn.classList.add('active');
    soundOff.classList.remove('active');
    
    if (backgroundAudio) {
      backgroundAudio.volume = 0.05;
      setTimeout(() => {
        backgroundAudio.play().catch(error => console.log('Background audio failed:', error));
      }, 2000);
    }
  }

  // Function to initialize sound effects for interactive elements
  function initSoundEffects() {
    const interactiveElements = document.querySelectorAll('a, button, .service-card');
    
    interactiveElements.forEach(element => {
      element.addEventListener('mouseenter', () => {
        if (audioEnabled && hoverSound) {
          hoverSound.volume = 0.1;
          hoverSound.currentTime = 0;
          hoverSound.play().catch(error => {});
        }
      });
      
      element.addEventListener('click', () => {
        if (audioEnabled && clickSound) {
          clickSound.volume = 0.2;
          clickSound.currentTime = 0;
          clickSound.play().catch(error => {});
        }
      });
    });
  }
  
  /* =======================
     SCROLL EFFECTS
  ======================= */
  let lastScrollPosition = 0;
  
  window.addEventListener('scroll', () => {
    const scrollPosition = window.scrollY;
    const header = document.querySelector('header');
    
    // Header show/hide effect
    if (scrollPosition > 100) {
      if (scrollPosition > lastScrollPosition) {
        // Scrolling down
        header.classList.add('hidden');
      } else {
        // Scrolling up
        header.classList.remove('hidden');
        header.classList.add('solid');
      }
    } else {
      header.classList.remove('solid');
      header.classList.remove('hidden');
    }
    
    lastScrollPosition = scrollPosition;
    
    // Parallax effect
    const parallax = document.querySelector('.parallax');
    if (parallax) {
      parallax.style.transform = `translateZ(-1px) scale(1.5) translateY(${scrollPosition * 0.3}px)`;
    }
    
    // Back to top button visibility
    if (scrollPosition > 500) {
      backToTopButton.classList.add('visible');
    } else {
      backToTopButton.classList.remove('visible');
    }
  });
  
  /* =======================
     BACK TO TOP
  ======================= */
  backToTopButton.addEventListener('click', () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
    
    // Play click sound
    if (audioEnabled && clickSound) {
      clickSound.currentTime = 0;
      clickSound.play();
    }
  });
  
  function initFAQAccordion() {
    console.log('initFAQAccordion called'); // Debug log
    const faqItems = document.querySelectorAll('.faq-item');
  
    if (faqItems.length > 0) {
      faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
  
        question.addEventListener('click', () => {
          console.log('FAQ item clicked'); // Debug log
          // Close all other items
          faqItems.forEach(otherItem => {
            if (otherItem !== item && otherItem.classList.contains('active')) {
              otherItem.classList.remove('active');
            }
          });
  
          // Toggle current item
          item.classList.toggle('active');
  
          // Play sound effect if enabled
          if (typeof audioEnabled !== 'undefined' && audioEnabled && clickSound) {
            clickSound.currentTime = 0;
            clickSound.play().catch(e => {
              console.error('Click sound play error:', e);
            });
          }
        });
      });
  
      // Make first FAQ item active by default
      console.log('Opening first FAQ item'); // Debug log
      faqItems[0].classList.add('active');
    } else {
      console.log('No FAQ items found'); // Debug log if no FAQ items exist
    }
  }

  /* =======================
     MODAL HANDLING
  ======================= */
function initShowreelModal() {
  console.log('initShowreelModal called'); // Debug log
  const showreelButton = document.querySelector('.play-showreel');
  const showreelModal = document.getElementById('showreel-modal');
  console.log('showreelButton:', showreelButton); // Debug log
  console.log('showreelModal:', showreelModal); // Debug log

  if (showreelButton && showreelModal) {
    const modalClose = showreelModal.querySelector('.modal-close');
    const modalOverlay = showreelModal.querySelector('.modal-overlay');
    const video = showreelModal.querySelector('#showreel-video');

    showreelButton.addEventListener('click', (e) => {
      e.preventDefault();
      console.log('Showreel button clicked'); // Debug log
      showreelModal.classList.add('active');
      body.style.overflow = 'hidden';

      if (video) {
        setTimeout(() => {
          console.log('Attempting to play video'); // Debug log
          video.currentTime = 0; // Reset to start
          video.play().catch(error => {
            console.error('Video play error:', error); // Log any errors
          });
        }, 100);
      }

      if (audioEnabled && clickSound) {
        clickSound.currentTime = 0;
        clickSound.play();
      }
    });

    // Ensure modalClose event listener is working
    if (modalClose) {
      modalClose.addEventListener('click', () => {
        console.log('Close button clicked'); // Debug log
        showreelModal.classList.remove('active');
        body.style.overflow = '';
        if (video) {
          video.pause();
          video.currentTime = 0;
        }
      });
    } else {
      console.error('modal-close element not found'); // Debug error
    }

    modalOverlay.addEventListener('click', () => {
      showreelModal.classList.remove('active');
      body.style.overflow = '';
      if (video) {
        video.pause();
        video.currentTime = 0;
      }
    });

    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && showreelModal.classList.contains('active')) {
        showreelModal.classList.remove('active');
        body.style.overflow = '';
        if (video) {
          video.pause();
          video.currentTime = 0;
        }
      }
    });
  } else {
    console.error('showreelButton or showreelModal not found'); // Debug error
  }
}  
  /* =======================
     TESTIMONIAL SLIDER
  ======================= */
  function initTestimonialSlider() {
    const testimonialSlider = document.querySelector('.testimonials-slider');
    
    if (testimonialSlider) {
      const slides = testimonialSlider.querySelectorAll('.testimonial-slide');
      const prevButton = testimonialSlider.querySelector('.control-prev');
      const nextButton = testimonialSlider.querySelector('.control-next');
      const indicators = testimonialSlider.querySelectorAll('.indicator');
      let currentSlide = 0;
      
      function showSlide(index) {
        slides.forEach((slide, i) => {
          slide.classList.remove('active');
          indicators[i].classList.remove('active');
        });
        
        slides[index].classList.add('active');
        indicators[index].classList.add('active');
      }
      
      prevButton.addEventListener('click', () => {
        currentSlide = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(currentSlide);
        
        // Play click sound
        if (audioEnabled && clickSound) {
          clickSound.currentTime = 0;
          clickSound.play();
        }
      });
      
      nextButton.addEventListener('click', () => {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
        
        // Play click sound
        if (audioEnabled && clickSound) {
          clickSound.currentTime = 0;
          clickSound.play();
        }
      });
      
      indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
          currentSlide = index;
          showSlide(currentSlide);
          
          // Play click sound
          if (audioEnabled && clickSound) {
            clickSound.currentTime = 0;
            clickSound.play();
          }
        });
      });
      
      // Auto-advance slides
      let slideInterval = setInterval(() => {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
      }, 6000);
      
      // Pause auto-advance on hover
      testimonialSlider.addEventListener('mouseenter', () => {
        clearInterval(slideInterval);
      });
      
      testimonialSlider.addEventListener('mouseleave', () => {
        slideInterval = setInterval(() => {
          currentSlide = (currentSlide + 1) % slides.length;
          showSlide(currentSlide);
        }, 6000);
      });
    }
  }
  
/* =======================
   FORM VALIDATION - MODIFIED VERSION
======================= */
function initFormValidation() {
  if (contactForm) {
    console.log('initFormValidation: Attaching field validation only');
    const formFields = contactForm.querySelectorAll('input, textarea');
    
    formFields.forEach(field => {
      // Validate on blur
      field.addEventListener('blur', () => {
        validateField(field);
      });
      
      // Clear validation on focus
      field.addEventListener('focus', () => {
        const validationElement = field.parentElement.querySelector('.form-validation');
        if (validationElement) {
          validationElement.textContent = '';
          field.parentElement.classList.remove('error');
        }
      });
    });
    
    // DO NOT ADD SUBMIT HANDLER HERE
    // The submit handler is now defined in contact.html
    
    // Export the validation function for use by other scripts
    window.validateContactFields = function() {
      let isValid = true;
      
      // Validate all fields
      formFields.forEach(field => {
        if (field.hasAttribute('required')) {
          const fieldIsValid = validateField(field);
          if (!fieldIsValid) {
            isValid = false;
          }
        }
      });
      
      return isValid;
    };
  }
  
  function validateField(field) {
    const validationElement = field.parentElement.querySelector('.form-validation');
    let isValid = true;
    
    if (!validationElement) return true;
    
    if (field.hasAttribute('required') && !field.value.trim()) {
      validationElement.textContent = 'This field is required';
      field.parentElement.classList.add('error');
      isValid = false;
    } else if (field.type === 'email' && field.value.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(field.value)) {
        validationElement.textContent = 'Please enter a valid email address';
        field.parentElement.classList.add('error');
        isValid = false;
      }
    }
    
    return isValid;
  }
}
  
  /* =======================
     SCROLL ANIMATIONS
  ======================= */
  function initAnimations() {
    // Only initialize GSAP on non-mobile devices for better performance
    if (typeof gsap !== 'undefined') {
      // Register ScrollTrigger plugin
      gsap.registerPlugin(ScrollTrigger);
      
      // Stagger animations for service cards
      gsap.from('.service-card', {
        y: 50,
        opacity: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: '.services-showcase',
          start: 'top 80%',
        }
      });
      
      // About section animations
      gsap.from('.about-image', {
        x: isMedium ? 0 : -50,
        y: isMedium ? 50 : 0,
        opacity: 0,
        duration: 1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: '.about-content',
          start: 'top 70%',
        }
      });
      
      gsap.from('.about-text', {
        x: isMedium ? 0 : 50,
        y: isMedium ? 50 : 0,
        opacity: 0,
        duration: 1,
        delay: 0.3,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: '.about-content',
          start: 'top 70%',
        }
      });
      
      // Count up animation for stats
      const stats = document.querySelectorAll('.stat-number');
      
      stats.forEach(stat => {
        const targetValue = parseInt(stat.getAttribute('data-count'));
        
        gsap.from(stat, {
          innerText: 0,
          duration: 2,
          ease: 'power2.out',
          snap: { innerText: 1 },
          scrollTrigger: {
            trigger: '.about-stats',
            start: 'top 80%',
          },
          onUpdate: function() {
            stat.innerText = Math.round(this.targets()[0].innerText);
          }
        });
      });
      
      // Timeline nodes animation
      gsap.from('.timeline-node', {
        x: -50,
        opacity: 0,
        duration: 0.8,
        stagger: 0.15,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: '.process-timeline',
          start: 'top 70%',
        }
      });
      
      // Contact section animations
      gsap.from('.contact-info', {
        x: isMedium ? 0 : -50,
        y: isMedium ? 50 : 0,
        opacity: 0,
        duration: 1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: '.contact-content',
          start: 'top 70%',
        }
      });
      
      gsap.from('.contact-form-container', {
        x: isMedium ? 0 : 50,
        y: isMedium ? 50 : 0,
        opacity: 0,
        duration: 1,
        delay: 0.3,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: '.contact-content',
          start: 'top 70%',
        }
      });
    }
  }
  
  /* =======================
     THREE.JS ANIMATIONS
  ======================= */
  function initThreeJS() {
    const threeCanvas = document.getElementById('three-canvas');
    
    if (!threeCanvas || typeof THREE === 'undefined') return;
    
    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 5;
    
    const renderer = new THREE.WebGLRenderer({
      canvas: threeCanvas,
      antialias: true,
      alpha: true
    });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    
    // Create central element
    const centralGeometry = new THREE.SphereGeometry(1, 32, 32);
    const centralMaterial = new THREE.MeshBasicMaterial({
      color: 0x4169e1,
      transparent: true,
      opacity: 0.1,
      wireframe: true
    });
    const centralSphere = new THREE.Mesh(centralGeometry, centralMaterial);
    scene.add(centralSphere);
    
    // Create orbiting elements
    const orbitingElements = [];
    const orbitCount = isMobile ? 10 : 20;
    
    for (let i = 0; i < orbitCount; i++) {
      const size = Math.random() * 0.2 + 0.05;
      const geometry = new THREE.SphereGeometry(size, 16, 16);
      
      const material = new THREE.MeshBasicMaterial({
        color: 0x4169e1,
        transparent: true,
        opacity: Math.random() * 0.5 + 0.2
      });
      
      const element = new THREE.Mesh(geometry, material);
      
      // Random position on sphere
      const radius = Math.random() * 2 + 2;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      
      element.position.x = radius * Math.sin(phi) * Math.cos(theta);
      element.position.y = radius * Math.sin(phi) * Math.sin(theta);
      element.position.z = radius * Math.cos(phi);
      
      // Random rotation speed
      element.userData = {
        rotationSpeed: {
          x: (Math.random() - 0.5) * 0.01,
          y: (Math.random() - 0.5) * 0.01,
          z: (Math.random() - 0.5) * 0.01
        },
        orbitAxis: new THREE.Vector3(
          Math.random() - 0.5,
          Math.random() - 0.5,
          Math.random() - 0.5
        ).normalize(),
        orbitSpeed: Math.random() * 0.005 + 0.002,
        orbitRadius: radius
      };
      
      scene.add(element);
      orbitingElements.push(element);
    }
    
    // Create particle system for background
    const particleCount = isMobile ? 500 : 1000;
    const particles = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);
    const particleSizes = new Float32Array(particleCount);
    
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      particlePositions[i3] = (Math.random() - 0.5) * 20;
      particlePositions[i3 + 1] = (Math.random() - 0.5) * 20;
      particlePositions[i3 + 2] = (Math.random() - 0.5) * 20;
      
      particleSizes[i] = Math.random() * 2 + 1;
    }
    
    particles.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
    particles.setAttribute('size', new THREE.BufferAttribute(particleSizes, 1));
    
    const particleMaterial = new THREE.PointsMaterial({
      color: 0x4169e1,
      size: 0.05,
      transparent: true,
      opacity: 0.6,
      sizeAttenuation: true
    });
    
    const particleSystem = new THREE.Points(particles, particleMaterial);
    scene.add(particleSystem);
    
    // Animation function
    function animate() {
      requestAnimationFrame(animate);
      
      // Rotate central sphere
      centralSphere.rotation.x += 0.001;
      centralSphere.rotation.y += 0.002;
      
      // Animate orbiting elements
      orbitingElements.forEach(element => {
        const userData = element.userData;
        
        // Rotate element around its own axis
        element.rotation.x += userData.rotationSpeed.x;
        element.rotation.y += userData.rotationSpeed.y;
        element.rotation.z += userData.rotationSpeed.z;
        
        // Orbit element around central sphere
        const orbitQuaternion = new THREE.Quaternion();
        orbitQuaternion.setFromAxisAngle(userData.orbitAxis, userData.orbitSpeed);
        
        element.position.applyQuaternion(orbitQuaternion);
      });
      
      // Slowly rotate particle system
      particleSystem.rotation.x += 0.0001;
      particleSystem.rotation.y += 0.0002;
      
      // Apply pulsating effect to central sphere
      const time = Date.now() * 0.001;
      centralSphere.scale.setScalar(1 + Math.sin(time) * 0.1);
      
      // Render the scene
      renderer.render(scene, camera);
    }
    
    // Handle window resize
    window.addEventListener('resize', () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    });
    
    // Start animation
    animate();
  }
  
  /* =======================
     PARTICLE BACKGROUND
  ======================= */
  function initParticles() {
    // Skip on mobile for performance
    if (isMobile) return;
    
    // Create canvas
    const canvas = document.createElement('canvas');
    canvas.id = 'particles-background';
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '-1';
    document.body.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    
    // Set canvas dimensions
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    
    // Particle class
    class Particle {
      constructor() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.size = Math.random() * 2 + 1;
        this.speedX = (Math.random() - 0.5) * 0.5;
        this.speedY = (Math.random() - 0.5) * 0.5;
        this.color = '#4169e1';
        this.opacity = Math.random() * 0.5 + 0.1;
      }
      
      update() {
        this.x += this.speedX;
        this.y += this.speedY;
        
        // Boundaries check
        if (this.x < 0 || this.x > canvas.width) {
          this.speedX = -this.speedX;
        }
        
        if (this.y < 0 || this.y > canvas.height) {
          this.speedY = -this.speedY;
        }
      }
      
      draw() {
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.globalAlpha = this.opacity;
        ctx.fill();
      }
    }
    
    // Create particles
    const particleCount = Math.min(Math.floor(window.innerWidth * 0.05), 100);
    const particles = [];
    
    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle());
    }
    
    // Mouse interaction
    let mouse = {
      x: null,
      y: null,
      radius: 150
    };
    
    window.addEventListener('mousemove', (e) => {
      mouse.x = e.clientX;
      mouse.y = e.clientY;
    });
    
    // Animation function
    function animate() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Update and draw particles
      for (let i = 0; i < particles.length; i++) {
        particles[i].update();
        particles[i].draw();
      }
      
      // Connect particles with lines
      connectParticles();
      
      requestAnimationFrame(animate);
    }
    
    // Connect nearby particles with lines
    function connectParticles() {
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 150) {
            // Calculate opacity based on distance
            const opacity = 1 - (distance / 150);
            
            ctx.beginPath();
            ctx.strokeStyle = '#4169e1';
            ctx.globalAlpha = opacity * 0.2;
            ctx.lineWidth = 1;
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.stroke();
          }
        }
        
        // Connect to mouse position if near
        if (mouse.x !== null && mouse.y !== null) {
          const dx = particles[i].x - mouse.x;
          const dy = particles[i].y - mouse.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < mouse.radius) {
            const opacity = 1 - (distance / mouse.radius);
            
            ctx.beginPath();
            ctx.strokeStyle = '#4169e1';
            ctx.globalAlpha = opacity * 0.5;
            ctx.lineWidth = 1;
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(mouse.x, mouse.y);
            ctx.stroke();
          }
        }
      }
    }
    
    // Handle window resize
    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    });
    
    // Start animation
    animate();
  }
  
  /* =======================
     SMOOTH SCROLLING
  ======================= */
  function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        if (this.getAttribute('href') !== '#') {
          e.preventDefault();
          
          const target = document.querySelector(this.getAttribute('href'));
          
          if (target) {
            window.scrollTo({
              top: target.offsetTop - 80,
              behavior: 'smooth'
            });
            
            // Close mobile menu if open
            if (nav.classList.contains('active')) {
              navToggle.classList.remove('active');
              nav.classList.remove('active');
              body.style.overflow = '';
            }
          }
        }
      });
    });
  }
});
