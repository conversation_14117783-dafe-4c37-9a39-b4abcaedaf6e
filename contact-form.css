/* Contact Form Styles */

/* Form validation styling */
.form-group {
    position: relative;
    margin-bottom: 2rem;
  }
  
  .form-group input,
  .form-group textarea,
  .form-group select {
    width: 100%;
    padding: 1.5rem;
    border: 1px solid rgba(65, 105, 225, 0.2);
    border-radius: 0.8rem;
    background: var(--tertiary-bg);
    color: var(--text-primary);
    font-size: 1.6rem;
    transition: all 0.3s ease;
  }
  
  .form-group input:focus,
  .form-group textarea:focus,
  .form-group select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.1);
  }
  
  .form-group input.error,
  .form-group textarea.error,
  .form-group select.error {
    border-color: #ef476f;
    box-shadow: 0 0 0 3px rgba(239, 71, 111, 0.1);
  }
  
  .form-validation {
    position: absolute;
    bottom: -2rem;
    left: 0;
    font-size: 1.2rem;
    color: #ef476f;
    transition: all 0.3s ease;
    height: 1.8rem;
  }
  
  .form-status {
    padding: 1.5rem;
    border-radius: 0.8rem;
    margin-top: 2rem;
    font-size: 1.6rem;
    text-align: center;
    opacity: 0;
    transition: opacity 0.5s ease;
  }
  
  .form-status.success {
    background: rgba(15, 224, 106, 0.1);
    color: #0fe06a;
    border: 1px solid rgba(15, 224, 106, 0.2);
  }
  
  .form-status.error {
    background: rgba(239, 71, 111, 0.1);
    color: #ef476f;
    border: 1px solid rgba(239, 71, 111, 0.2);
  }
  
  .form-submit {
    width: 100%;
    position: relative;
    overflow: hidden;
  }
  
  .form-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  .form-submit:disabled:hover {
    transform: none;
  }
  
  /* Loading spinner for submit button */
  .form-submit.loading .button-text::after {
    content: "";
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-left: 1rem;
    vertical-align: middle;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  /* Enhanced select styling */
  select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%234169e1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1.5rem center;
    background-size: 1.6rem;
    padding-right: 4rem;
  }
  
  /* Placeholder styling */
  ::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
  }
  
  /* Animation for successful form submission */
  @keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }
  
  .form-status.success {
    animation: successPulse 1s ease-in-out;
  }