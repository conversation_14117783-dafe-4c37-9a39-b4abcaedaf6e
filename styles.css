/* Modern Font Imports */
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css');

/* CSS Variables for Theming */
:root {
  /* Colors - Dark Theme (Default) */
  --primary-bg: #0a0a14;
  --secondary-bg: #0f0f1e;
  --tertiary-bg: #141428;
  --accent-color: #4169e1;
  --accent-hover: #5a7be8;
  --text-primary: #ffffff;
  --text-secondary: #b0b0c0;
  --text-muted: #70708c;
  --border-color: rgba(65, 105, 225, 0.2);
  --success-color: #0fe06a;
  --warning-color: #ffd166;
  --error-color: #ef476f;
  --gradient-primary: linear-gradient(135deg, #4169e1, #7c41e1);
  --gradient-dark: linear-gradient(135deg, #0a0a14, #141428);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.25);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.3);
  --header-height: 70px;
  --animation-slow: 0.5s;
  --animation-medium: 0.3s;
  --animation-fast: 0.15s;
  
  /* Font Settings */
  --font-heading: 'Space Grotesk', sans-serif;
  --font-body: 'Inter', sans-serif;
}

/* Light Theme Variables */
[data-theme="light"] {
  --primary-bg: #ffffff;
  --secondary-bg: #f4f4f8;
  --tertiary-bg: #e8e8f0;
  --text-primary: #0a0a14;
  --text-secondary: #333345;
  --text-muted: #5d5d7a;
  --border-color: rgba(65, 105, 225, 0.2);
  --gradient-dark: linear-gradient(135deg, #f4f4f8, #e8e8f0);
}

/* Base Reset */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 62.5%; /* 10px base for easier rems */
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: var(--accent-color) var(--tertiary-bg);
}

body {
  font-family: var(--font-body);
  font-size: 1.6rem;
  line-height: 1.7;
  color: var(--text-primary);
  background-color: var(--primary-bg);
  overflow-x: hidden;
  transition: background-color 0.5s ease, color 0.5s ease;
  padding-top: var(--header-height);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--tertiary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-hover);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2rem;
  color: var(--text-primary);
}

h1 {
  font-size: 6rem;
  letter-spacing: -0.02em;
}

h2 {
  font-size: 4.2rem;
  letter-spacing: -0.01em;
}

h3 {
  font-size: 2.8rem;
}

h4 {
  font-size: 2.2rem;
}

p {
  margin-bottom: 1.6rem;
  color: var(--text-secondary);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color var(--animation-medium) ease, transform var(--animation-medium) ease;
}

a:hover {
  color: var(--accent-hover);
}

ul, ol {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

/* Preloader */
#preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loader {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loader svg {
  width: 8rem;
  height: 8rem;
}

#loader-circle {
  fill: none;
  stroke: var(--accent-color);
  stroke-width: 2;
  stroke-dasharray: 200;
  stroke-dashoffset: 200;
  animation: dash 2s ease-in-out infinite;
}

.loader-text {
  margin-top: 2rem;
  font-family: var(--font-heading);
  font-size: 1.8rem;
  letter-spacing: 0.2em;
  color: var(--accent-color);
  font-weight: 700;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes dash {
  0% {
    stroke-dashoffset: 200;
  }
  50% {
    stroke-dashoffset: 50;
  }
  100% {
    stroke-dashoffset: 200;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* Custom Cursor */
.cursor-dot,
.cursor-outline {
  pointer-events: none;
  position: fixed;
  top: 0;
  left: 0;
  border-radius: 50%;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cursor-dot {
  width: 8px;
  height: 8px;
  background-color: var(--accent-color);
  transform: translate(-50%, -50%);
  transition: transform 0.1s ease;
}

.cursor-outline {
  width: 40px;
  height: 40px;
  border: 2px solid rgba(65, 105, 225, 0.5);
  transform: translate(-50%, -50%);
  transition: all 0.2s ease;
}

/* Header */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--header-height);
  background: rgba(10, 10, 20, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: background 0.3s ease, transform 0.3s ease;
}

[data-theme="light"] header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

header.hidden {
  transform: translateY(-100%);
}

header.solid {
  background: var(--primary-bg);
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 4rem;
  transition: transform 0.3s ease;
}

.logo-img:hover {
  transform: scale(1.1);
}

/* Navigation */
nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-toggle {
  display: none;
  cursor: pointer;
  width: 3rem;
  height: 3rem;
  position: relative;
  z-index: 1010;
}

.hamburger {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  cursor: pointer;
}

.hamburger span {
  display: block;
  width: 100%;
  height: 2px;
  background: var(--text-primary);
  transition: all 0.3s ease;
}

.nav-toggle.active .hamburger span:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}

.nav-toggle.active .hamburger span:nth-child(2) {
  opacity: 0;
}

.nav-toggle.active .hamburger span:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}

.nav-links {
  display: flex;
  gap: 3rem;
  align-items: center;
}

.nav-link {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding: 0.5rem 1rem;
  position: relative;
  transition: color 0.3s ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--accent-color);
  transition: width 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Dropdown Menu */
.dropdown {
  position: relative;
}

.dropdown-wrapper {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  min-width: 20rem;
  background: var(--secondary-bg);
  border-radius: 0.8rem;
  padding: 1rem;
  box-shadow: var(--shadow-md);
  opacity: 0;
  visibility: hidden;
  transform-origin: top center;
  transform: translateX(-50%) translateY(1rem);
  transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
  z-index: 100;
}

.dropdown:hover .dropdown-wrapper {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

.dropdown-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.dropdown-link {
  font-size: 1.3rem;
  padding: 0.8rem 1.2rem;
  border-radius: 0.4rem;
  display: flex;
  align-items: center;
  transition: background 0.3s ease, color 0.3s ease;
}

.dropdown-link span {
  display: inline-block;
  width: 2.5rem;
  color: var(--accent-color);
  font-family: var(--font-heading);
  font-weight: 600;
}

.dropdown-link:hover {
  background: var(--tertiary-bg);
  color: var(--text-primary);
}

/* Theme Toggle */
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  cursor: pointer;
  position: relative;
  margin-left: 2rem;
}

.theme-icon {
  position: absolute;
  transition: opacity 0.3s ease, transform 0.5s ease;
  font-size: 1.8rem;
}

.theme-icon.light-icon {
  opacity: 0;
  transform: rotate(90deg);
}

.theme-icon.dark-icon {
  opacity: 1;
  transform: rotate(0);
}

[data-theme="light"] .theme-icon.light-icon {
  opacity: 1;
  transform: rotate(0);
}

[data-theme="light"] .theme-icon.dark-icon {
  opacity: 0;
  transform: rotate(-90deg);
}

/* Hero Section */
#hero {
  position: relative;
  height: 100vh;
  min-height: 60rem;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: var(--gradient-dark);
}

#three-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.parallax {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('assets/parallax-bg.jpg') no-repeat center center/cover;
  opacity: 0.2;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 5rem;
}

.hero-text {
  max-width: 60rem;
}

.hero-text h1 {
  position: relative;
  margin-bottom: 1.5rem;
}

/* Glitch Effect */
.glitch-effect {
  position: relative;
  color: var(--text-primary);
}

.glitch-effect::before,
.glitch-effect::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  clip: rect(0, 0, 0, 0);
}

.glitch-effect::before {
  left: -2px;
  text-shadow: 2px 0 var(--accent-color);
  animation: glitch-1 2s infinite linear alternate-reverse;
}

.glitch-effect::after {
  left: 2px;
  text-shadow: -2px 0 var(--accent-hover);
  animation: glitch-2 3s infinite linear alternate-reverse;
}

@keyframes glitch-1 {
  0% {
    clip: rect(36px, 9999px, 28px, 0);
  }
  5% {
    clip: rect(85px, 9999px, 95px, 0);
  }
  10% {
    clip: rect(12px, 9999px, 48px, 0);
  }
  15% {
    clip: rect(64px, 9999px, 78px, 0);
  }
  20% {
    clip: rect(30px, 9999px, 16px, 0);
  }
  25% {
    clip: rect(71px, 9999px, 88px, 0);
  }
  30% {
    clip: rect(10px, 9999px, 85px, 0);
  }
  35% {
    clip: rect(80px, 9999px, 10px, 0);
  }
  40% {
    clip: rect(92px, 9999px, 59px, 0);
  }
  45% {
    clip: rect(23px, 9999px, 70px, 0);
  }
  50% {
    clip: rect(38px, 9999px, 92px, 0);
  }
  55% {
    clip: rect(3px, 9999px, 24px, 0);
  }
  60% {
    clip: rect(26px, 9999px, 75px, 0);
  }
  65% {
    clip: rect(73px, 9999px, 52px, 0);
  }
  70% {
    clip: rect(84px, 9999px, 33px, 0);
  }
  75% {
    clip: rect(66px, 9999px, 24px, 0);
  }
  80% {
    clip: rect(40px, 9999px, 70px, 0);
  }
  85% {
    clip: rect(29px, 9999px, 90px, 0);
  }
  90% {
    clip: rect(18px, 9999px, 40px, 0);
  }
  95% {
    clip: rect(77px, 9999px, 62px, 0);
  }
  100% {
    clip: rect(14px, 9999px, 86px, 0);
  }
}

@keyframes glitch-2 {
  0% {
    clip: rect(19px, 9999px, 42px, 0);
  }
  5% {
    clip: rect(68px, 9999px, 10px, 0);
  }
  10% {
    clip: rect(9px, 9999px, 87px, 0);
  }
  15% {
    clip: rect(18px, 9999px, 69px, 0);
  }
  20% {
    clip: rect(87px, 9999px, 22px, 0);
  }
  25% {
    clip: rect(5px, 9999px, 51px, 0);
  }
  30% {
    clip: rect(36px, 9999px, 40px, 0);
  }
  35% {
    clip: rect(2px, 9999px, 46px, 0);
  }
  40% {
    clip: rect(51px, 9999px, 72px, 0);
  }
  45% {
    clip: rect(7px, 9999px, 30px, 0);
  }
  50% {
    clip: rect(65px, 9999px, 99px, 0);
  }
  55% {
    clip: rect(75px, 9999px, 56px, 0);
  }
  60% {
    clip: rect(82px, 9999px, 31px, 0);
  }
  65% {
    clip: rect(54px, 9999px, 81px, 0);
  }
  70% {
    clip: rect(20px, 9999px, 65px, 0);
  }
  75% {
    clip: rect(15px, 9999px, 84px, 0);
  }
  80% {
    clip: rect(92px, 9999px, 3px, 0);
  }
  85% {
    clip: rect(83px, 9999px, 24px, 0);
  }
  90% {
    clip: rect(57px, 9999px, 78px, 0);
  }
  95% {
    clip: rect(70px, 9999px, 41px, 0);
  }
  100% {
    clip: rect(35px, 9999px, 73px, 0);
  }
}

/* Typewriter Effect */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--accent-color);
  white-space: nowrap;
  margin: 0 auto;
  letter-spacing: 0.05em;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
  animation-delay: 1s;
  animation-fill-mode: both;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: var(--accent-color) }
}

/* Hero CTA */
.hero-cta {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  margin-top: 3rem;
}

/* CTA Buttons */
.cta-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1.2rem 2.4rem;
  border-radius: 4rem;
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.6rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  position: relative;
  overflow: hidden;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%) skewX(-15deg);
  transition: transform 0.5s ease;
}

.cta-button:hover::before {
  transform: translateX(100%) skewX(-15deg);
}

.cta-button.primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: 0 4px 12px rgba(65, 105, 225, 0.3);
}

.cta-button.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(65, 105, 225, 0.4);
}

.cta-button.secondary {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--accent-color);
}

.cta-button.secondary:hover {
  background: rgba(65, 105, 225, 0.1);
  transform: translateY(-3px);
}

.cta-button.tertiary {
  background: var(--tertiary-bg);
  color: var(--text-primary);
}

.cta-button.tertiary:hover {
  background: var(--secondary-bg);
  transform: translateY(-3px);
}

.button-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.cta-button:hover .button-icon {
  transform: translateX(5px);
}

.play-icon {
  font-size: 1.4rem;
}

/* Hero Highlights */
.hero-highlights {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.highlight-card {
  flex: 1;
  min-width: 20rem;
  background: rgba(10, 10, 20, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.highlight-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.highlight-icon {
  width: 5rem;
  height: 5rem;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.2rem;
  color: white;
}

.highlight-text h3 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.highlight-text p {
  font-size: 1.4rem;
  margin-bottom: 0;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 4rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  z-index: 2;
  animation: bounce 2s infinite;
}

.mouse {
  width: 3rem;
  height: 5rem;
  border: 2px solid var(--text-primary);
  border-radius: 2rem;
  position: relative;
}

.mouse-wheel {
  width: 0.6rem;
  height: 0.6rem;
  background: var(--text-primary);
  border-radius: 50%;
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  animation: scroll 1.5s infinite;
}

.scroll-text {
  font-size: 1.2rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: var(--text-secondary);
}

@keyframes scroll {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(1.5rem);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Section Styles */
.section {
  padding: 10rem 0;
  position: relative;
  overflow: hidden;
}

.section-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(rgba(65, 105, 225, 0.1) 2px, transparent 2px),
    radial-gradient(rgba(65, 105, 225, 0.05) 2px, transparent 2px);
  background-size: 50px 50px;
  background-position: 0 0, 25px 25px;
  z-index: -1;
  opacity: 0.5;
}

.section-header {
  text-align: center;
  max-width: 80rem;
  margin: 0 auto 6rem;
}

.section-subtitle {
  display: inline-block;
  font-family: var(--font-heading);
  font-size: 1.6rem;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  color: var(--accent-color);
  margin-bottom: 1.5rem;
  position: relative;
}

.section-subtitle::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 5rem;
  height: 2px;
  background: var(--accent-color);
}

.section-title {
  margin-bottom: 2rem;
}

.section-description {
  font-size: 1.8rem;
  color: var(--text-secondary);
}

/* Services Section */
.services-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
  gap: 3rem;
  margin-bottom: 4rem;
}

.service-card {
  position: relative;
  background: var(--secondary-bg);
  border-radius: 1rem;
  padding: 3rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  z-index: 1;
  overflow: hidden;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.service-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.service-card:hover .service-background {
  opacity: 0.05;
}

.service-icon {
  width: 6rem;
  height: 6rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: var(--accent-color);
  margin-bottom: 2rem;
  transition: transform 0.3s ease, background 0.3s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.1);
  background: var(--accent-color);
  color: white;
}

.service-card h3 {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
}

.service-card p {
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

.service-features {
  margin-bottom: 2.5rem;
}

.service-features li {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  color: var(--text-secondary);
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--accent-color);
}

.service-link {
  display: inline-flex;
  align-items: center;
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.5rem;
  color: var(--accent-color);
}

.service-link::after {
  content: '→';
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.service-link:hover::after {
  transform: translateX(5px);
}

.section-cta {
  text-align: center;
  margin-top: 2rem;
}

/* About Section */
.about-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 5rem;
  align-items: center;
}

.about-image-container {
  position: relative;
}

.about-image {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0.2;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: -5rem;
  position: relative;
  z-index: 2;
}

.stat-item {
  background: var(--secondary-bg);
  border-radius: 0.8rem;
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-md);
}

.stat-number {
  font-family: var(--font-heading);
  font-size: 3.6rem;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
}

.stat-symbol {
  font-family: var(--font-heading);
  font-size: 2.4rem;
  font-weight: 700;
  color: var(--accent-color);
}

.stat-label {
  font-size: 1.3rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
  display: block;
}

.about-text {
  padding-right: 2rem;
}

.about-features {
  margin: 3rem 0;
}

.feature-item {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.feature-icon {
  flex-shrink: 0;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: rgba(65, 105, 225, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  font-size: 1.8rem;
}

.feature-text h4 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.feature-text p {
  font-size: 1.5rem;
  margin-bottom: 0;
}

/* Process Timeline */
.process-timeline {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 80rem;
  margin: 0 auto;
  position: relative;
}

.process-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 4.5rem;
  width: 2px;
  height: 100%;
  background: var(--accent-color);
  opacity: 0.3;
}

.timeline-node {
  display: flex;
  gap: 3rem;
  position: relative;
  padding-left: 10rem;
}

.node-number {
  position: absolute;
  left: 0;
  top: 0;
  width: 9rem;
  height: 5rem;
  background: var(--secondary-bg);
  border-radius: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-heading);
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-color);
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease, background 0.3s ease, color 0.3s ease;
}

.timeline-node:hover .node-number {
  transform: scale(1.1);
  background: var(--accent-color);
  color: white;
}

.node-content {
  flex: 1;
  background: var(--secondary-bg);
  border-radius: 1rem;
  padding: 2rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.timeline-node:hover .node-content {
  transform: translateX(10px);
  box-shadow: var(--shadow-md);
}

.node-content h3 {
  font-size: 2.2rem;
  margin-bottom: 1rem;
}

.node-content p {
  font-size: 1.5rem;
  margin-bottom: 0;
}

/* Testimonials */
.testimonials-slider {
  position: relative;
  max-width: 80rem;
  margin: 0 auto;
}

.testimonial-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  opacity: 0;
  visibility: hidden;
  transform: translateX(50px);
  transition: opacity 0.5s ease, visibility 0.5s ease, transform 0.5s ease;
}

.testimonial-slide.active {
  position: relative;
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.testimonial-card {
  background: var(--secondary-bg);
  border-radius: 1rem;
  padding: 4rem;
  box-shadow: var(--shadow-md);
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: 2rem;
  left: 2rem;
  font-family: var(--font-heading);
  font-size: 12rem;
  line-height: 1;
  color: var(--accent-color);
  opacity: 0.1;
}

.testimonial-quote {
  font-size: 2rem;
  font-style: italic;
  color: var(--text-primary);
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.author-image {
  width: 6rem;
  height: 6rem;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--accent-color);
}

.author-info h4 {
  font-size: 1.8rem;
  margin-bottom: 0.2rem;
}

.author-info p {
  font-size: 1.4rem;
  color: var(--text-secondary);
  margin-bottom: 0;
}

.testimonial-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  margin-top: 4rem;
}

.control-prev,
.control-next {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: var(--tertiary-bg);
  border: none;
  font-size: 1.8rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: background 0.3s ease, transform 0.3s ease;
}

.control-prev:hover,
.control-next:hover {
  background: var(--accent-color);
  color: white;
  transform: scale(1.1);
}

.control-indicators {
  display: flex;
  gap: 1rem;
}

.indicator {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: var(--tertiary-bg);
  cursor: pointer;
  transition: background 0.3s ease, transform 0.3s ease;
}

.indicator.active {
  background: var(--accent-color);
  transform: scale(1.2);
}

/* Contact Section */
.contact-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 5rem;
  align-items: center;
}

.contact-methods {
  margin: 3rem 0;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.method-icon {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  background: rgba(65, 105, 225, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--accent-color);
}

.method-details h4 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.method-details p {
  font-size: 1.6rem;
  margin-bottom: 0;
}

.social-links {
  display: flex;
  gap: 1.5rem;
  margin-top: 3rem;
}

.social-link {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: var(--tertiary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--text-primary);
  transition: background 0.3s ease, transform 0.3s ease, color 0.3s ease;
}

.social-link:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-5px);
}

/* Contact Form */
.contact-form-container {
  background: var(--secondary-bg);
  border-radius: 1rem;
  padding: 4rem;
  box-shadow: var(--shadow-md);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-group {
  position: relative;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 1.5rem 2rem;
  border: 1px solid rgba(65, 105, 225, 0.2);
  border-radius: 0.8rem;
  background: var(--tertiary-bg);
  font-size: 1.6rem;
  color: var(--text-primary);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.2);
}

.form-group label {
  position: absolute;
  top: 1.5rem;
  left: 2rem;
  font-size: 1.6rem;
  color: var(--text-muted);
  transition: all 0.3s ease;
  pointer-events: none;
}

.form-group input:focus ~ label,
.form-group textarea:focus ~ label,
.form-group select:focus ~ label,
.form-group input:not(:placeholder-shown) ~ label,
.form-group textarea:not(:placeholder-shown) ~ label {
  top: -1rem;
  left: 1.5rem;
  background: var(--secondary-bg);
  padding: 0 0.5rem;
  font-size: 1.2rem;
  color: var(--accent-color);
}

.form-group textarea {
  min-height: 15rem;
  resize: vertical;
}

.form-group select {
  appearance: none;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234169e1'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 2rem;
}

.form-validation {
  font-size: 1.2rem;
  color: var(--error-color);
  margin-top: 0.5rem;
  min-height: 1.8rem;
}

.form-status {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 0.8rem;
  font-size: 1.6rem;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-status.success {
  background: rgba(15, 224, 106, 0.1);
  color: var(--success-color);
  opacity: 1;
}

.form-status.error {
  background: rgba(239, 71, 111, 0.1);
  color: var(--error-color);
  opacity: 1;
}

.form-submit {
  margin-top: 1rem;
}

/* Modal Styles */
/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  align-items: center;
  justify-content: center;
}

.modal.active {
  display: flex;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.modal-container {
  position: relative;
  max-width: 70vw; /* Reduced from 90vw */
  max-height: 70vh; /* Reduced from 90vh */
  width: 800px; /* Fixed width for consistency */
  height: 450px; /* Fixed height for 16:9 aspect ratio */
  background: var(--secondary-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 2rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: color 0.3s ease;
  z-index: 2; /* Ensure it’s above the video */
}

.modal-close:hover {
  color: var(--accent-color);
}

.video-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

#showreel-video {
  max-width: 100%;
  max-height: 100%;
  border-radius: 0.5rem;
  background: #000;
}
/* Footer */
footer {
  background: var(--secondary-bg);
  position: relative;
  z-index: 10;
}

.footer-main {
  padding: 6rem 0;
}

.footer-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 4rem;
}

.footer-info {
  max-width: 30rem;
}

.footer-logo {
  height: 4rem;
  margin-bottom: 2rem;
}

.footer-tagline {
  font-size: 1.5rem;
  margin-bottom: 2.5rem;
}

.footer-nav h4,
.footer-services h4,
.footer-contact h4 {
  font-size: 1.8rem;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 1rem;
}

.footer-nav h4::after,
.footer-services h4::after,
.footer-contact h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 5rem;
  height: 2px;
  background: var(--accent-color);
}

.footer-nav ul,
.footer-services ul {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-nav ul li a,
.footer-services ul li a {
  font-size: 1.5rem;
  color: var(--text-secondary);
  transition: color 0.3s ease, transform 0.3s ease;
  display: inline-block;
}

.footer-nav ul li a:hover,
.footer-services ul li a:hover {
  color: var(--text-primary);
  transform: translateX(5px);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.contact-item i {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background: rgba(65, 105, 225, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--accent-color);
}

.contact-item p {
  font-size: 1.5rem;
  margin-bottom: 0;
}

.footer-bottom {
  border-top: 1px solid rgba(65, 105, 225, 0.1);
  padding: 2rem 0;
}

.footer-bottom .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  font-size: 1.4rem;
  margin-bottom: 0;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.footer-links a {
  font-size: 1.4rem;
  color: var(--text-secondary);
}

/* Sound Toggle */
.sound-toggle {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  width: 4rem;
  height: 4rem;
  background: var(--secondary-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.sound-toggle:hover {
  transform: scale(1.1);
}

.sound-icon {
  position: absolute;
  font-size: 1.8rem;
  color: var(--text-primary);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.sound-icon.sound-on {
  opacity: 0;
  transform: scale(0);
}

.sound-icon.sound-off {
  opacity: 1;
  transform: scale(1);
}

.sound-icon.active {
  opacity: 1;
  transform: scale(1);
}

.sound-icon.sound-off.active {
  opacity: 0;
  transform: scale(0);
}

/* Floating CTA */
.floating-cta {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 100;
}

.floating-button {
  width: 6rem;
  height: 6rem;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.4rem;
  color: white;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
  animation: pulse-cta 2s infinite;
}

.floating-button:hover {
  transform: scale(1.1);
  animation-play-state: paused;
}

@keyframes pulse-cta {
  0% {
    box-shadow: 0 0 0 0 rgba(65, 105, 225, 0.5);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(65, 105, 225, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(65, 105, 225, 0);
  }
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: -5rem;
  right: 2rem;
  width: 4rem;
  height: 4rem;
  background: var(--secondary-bg);
  border-radius: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--text-primary);
  cursor: pointer;
  z-index: 99;
  transition: bottom 0.3s ease, background 0.3s ease;
  box-shadow: var(--shadow-md);
}

.back-to-top.visible {
  bottom: 9rem;
}

.back-to-top:hover {
  background: var(--accent-color);
  color: white;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  html {
    font-size: 58%;
  }
  
  .about-content,
  .contact-content {
    gap: 3rem;
  }
  
  .services-showcase {
    grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  }
}

@media (max-width: 992px) {
  html {
    font-size: 56%;
  }
  
  .about-content,
  .contact-content {
    grid-template-columns: 1fr;
  }
  
  .about-image-container {
    order: -1;
  }
  
  .footer-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  html {
    font-size: 54%;
  }
  
  .nav-toggle {
    display: block;
  }
  
  nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--primary-bg);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }
  
  nav.active {
    opacity: 1;
    visibility: visible;
  }
  
  .nav-links {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }
  
  .dropdown-wrapper {
    position: static;
    transform: none;
    opacity: 1;
    visibility: visible;
    background: transparent;
    box-shadow: none;
    padding: 0;
    margin-top: 1rem;
    width: 100%;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
  }
  
  .dropdown.active .dropdown-wrapper {
    max-height: 50rem;
  }
  
  .dropdown-content {
    grid-template-columns: 1fr;
  }
  
  .hero-content {
    padding-top: 3rem;
  }
  
  .hero-cta {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .section {
    padding: 6rem 0;
  }
  
  .process-timeline::before {
    left: 2.5rem;
  }
  
  .timeline-node {
    padding-left: 5rem;
  }
  
  .node-number {
    width: 5rem;
    height: 5rem;
    font-size: 1.6rem;
  }
  
  .testimonial-card {
    padding: 3rem;
  }
  
  .contact-form-container {
    padding: 3rem;
  }
}

@media (max-width: 576px) {
  html {
    font-size: 52%;
  }
  
  h1 {
    font-size: 4.2rem;
  }
  
  h2 {
    font-size: 3.2rem;
  }
  
  .footer-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-bottom .container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .services-showcase {
    grid-template-columns: 1fr;
  }
  
  .hero-highlights {
    flex-direction: column;
  }
  
  .highlight-card {
    min-width: 100%;
  }
  
  .about-stats {
    grid-template-columns: 1fr;
    margin-top: -3rem;
  }
}

/* Additional Styles for About Page */

/* Story Section */
.about-story {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
    align-items: center;
  }
  
  .story-content {
    padding-right: 2rem;
  }
  
  .story-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 3rem 0;
  }
  
  .story-quote {
    background: var(--tertiary-bg);
    border-left: 3px solid var(--accent-color);
    padding: 2rem;
    border-radius: 0.8rem;
    margin-top: 3rem;
  }
  
  .story-quote blockquote {
    font-size: 1.8rem;
    font-style: italic;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }
  
  .story-quote cite {
    font-size: 1.4rem;
    color: var(--accent-color);
  }
  
  .story-image {
    position: relative;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
  }
  
  /* Team Showcase */
  .team-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
    gap: 3rem;
  }
  
  .team-member-card {
    background: var(--secondary-bg);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .team-member-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
  }
  
  .member-image {
    position: relative;
    height: 25rem;
    overflow: hidden;
  }
  
  .member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .team-member-card:hover .member-image img {
    transform: scale(1.05);
  }
  
  .member-info {
    padding: 2rem;
  }
  
  .member-info h3 {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
  }
  
  .member-position {
    display: inline-block;
    font-size: 1.4rem;
    color: var(--accent-color);
    margin-bottom: 1.5rem;
  }
  
  .member-info p {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  .member-social {
    display: flex;
    gap: 1rem;
  }
  
  /* Values Section */
  .values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
    gap: 3rem;
  }
  
  .value-card {
    background: var(--secondary-bg);
    border-radius: 1rem;
    padding: 3rem;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .value-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
  }
  
  .value-icon {
    width: 7rem;
    height: 7rem;
    background: rgba(65, 105, 225, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--accent-color);
    margin: 0 auto 2rem;
    transition: background 0.3s ease, color 0.3s ease;
  }
  
  .value-card:hover .value-icon {
    background: var(--accent-color);
    color: white;
  }
  
  .value-card h3 {
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
  }
  
  .value-card p {
    font-size: 1.5rem;
  }
  
  /* Additional Styles for Contact Page */
  
  /* Contact Grid */
  .contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
  }
  
  .contact-details {
    display: flex;
    flex-direction: column;
    gap: 3rem;
  }
  
  .contact-map {
    position: relative;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    height: 25rem;
  }
  
  .contact-map img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .contact-info-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
  
  .info-card {
    background: var(--secondary-bg);
    border-radius: 1rem;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .info-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
  
  .info-icon {
    width: 5rem;
    height: 5rem;
    background: rgba(65, 105, 225, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--accent-color);
  }
  
  .info-content h3 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
  }
  
  .info-content p {
    font-size: 1.5rem;
    margin-bottom: 0;
  }
  
  .company-info {
    background: var(--secondary-bg);
    border-radius: 1rem;
    padding: 3rem;
  }
  
  .company-info h3 {
    font-size: 2.2rem;
    margin-bottom: 2rem;
  }
  
  .company-info p {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  /* Form Header */
  .form-header {
    margin-bottom: 3rem;
  }
  
  .form-header h3 {
    font-size: 2.4rem;
    margin-bottom: 1rem;
  }
  
  .form-header p {
    font-size: 1.6rem;
  }
  
  /* FAQ Section */
  .faq-container {
    max-width: 80rem;
    margin: 0 auto;
  }
  
  .faq-item {
    background: var(--secondary-bg);
    border-radius: 1rem;
    margin-bottom: 2rem;
    overflow: hidden;
  }
  
  .faq-question {
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }
  
  .faq-question h3 {
    font-size: 1.8rem;
    margin-bottom: 0;
  }
  
  .question-icon {
    width: 3rem;
    height: 3rem;
    background: rgba(65, 105, 225, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: var(--accent-color);
    transition: transform 0.3s ease, background 0.3s ease;
  }
  
  .faq-item.active .question-icon {
    transform: rotate(45deg);
    background: var(--accent-color);
    color: white;
  }
  
  .faq-answer {
    padding: 0 2rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
  }
  
  .faq-item.active .faq-answer {
    padding: 0 2rem 2rem;
    max-height: 50rem;
  }
  
  /* Additional Styles for Services Page */
  
  /* Services Tabs */
  .services-tabs {
    margin-bottom: 5rem;
  }
  
  .tabs-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 3rem;
    justify-content: center;
  }
  
  .tab-button {
    padding: 1.2rem 2.4rem;
    background: var(--tertiary-bg);
    border: none;
    border-radius: 3rem;
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .tab-button:hover {
    background: rgba(65, 105, 225, 0.1);
    color: var(--text-primary);
  }
  
  .tab-button.active {
    background: var(--accent-color);
    color: white;
  }
  
  /* Service Detail */
  .service-detail {
    margin-bottom: 10rem;
    scroll-margin-top: 10rem;
  }
  
  .service-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 4rem;
  }
  
  .service-number {
    font-family: var(--font-heading);
    font-size: 5rem;
    font-weight: 700;
    color: var(--accent-color);
    opacity: 0.3;
    line-height: 1;
  }
  
  .service-icon {
    width: 7rem;
    height: 7rem;
    background: rgba(65, 105, 225, 0.1);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--accent-color);
  }
  
  .service-header h2 {
    font-size: 3.6rem;
    margin-bottom: 0;
  }
  
  .service-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
    align-items: center;
  }
  
  .service-body:nth-of-type(even) {
    direction: rtl;
  }
  
  .service-body:nth-of-type(even) .service-description,
  .service-body:nth-of-type(even) .service-image {
    direction: ltr;
  }
  
  .service-description p {
    font-size: 1.6rem;
    margin-bottom: 3rem;
  }
  
  .service-image {
    position: relative;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
  }
  
  .service-image img {
    width: 100%;
    height: auto;
    display: block;
  }
  
  /* Services Summary */
  .services-summary {
    margin-top: 6rem;
  }
  
  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
    gap: 3rem;
  }
  
  .summary-card {
    background: var(--secondary-bg);
    border-radius: 1rem;
    padding: 3rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .summary-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
  }
  
  .card-icon {
    width: 6rem;
    height: 6rem;
    background: rgba(65, 105, 225, 0.1);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 2rem;
    transition: background 0.3s ease, color 0.3s ease;
  }
  
  .summary-card:hover .card-icon {
    background: var(--accent-color);
    color: white;
  }
  
  .summary-card h3 {
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
  }
  
  .summary-card p {
    font-size: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .summary-list {
    margin-left: 2rem;
  }
  
  .summary-list li {
    font-size: 1.4rem;
    color: var(--text-secondary);
    margin-bottom: 0.8rem;
    position: relative;
  }
  
  .summary-list li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: -1.5rem;
  }
  
  /* CTA Section */
  #cta {
    background: var(--gradient-primary);
    padding: 8rem 0;
    text-align: center;
    margin-top: 6rem;
  }
  
  .cta-content {
    max-width: 70rem;
    margin: 0 auto;
  }
  
  .cta-content h2 {
    color: white;
    font-size: 3.6rem;
    margin-bottom: 2rem;
  }
  
  .cta-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.8rem;
    margin-bottom: 3rem;
  }
  
  #cta .cta-button {
    background: white;
    color: var(--accent-color);
  }
  
  #cta .cta-button:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-3px);
  }
  
  /* Responsive Adjustments */
  @media (max-width: 992px) {
    .about-story,
    .service-body,
    .contact-grid {
      grid-template-columns: 1fr;
      gap: 3rem;
    }
  
    .contact-info-cards {
      grid-template-columns: 1fr;
    }
  
    .story-content {
      padding-right: 0;
    }
  
    .service-body:nth-of-type(even) {
      direction: ltr;
    }
  
    .service-header {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }
  
    .service-icon {
      margin: 0 auto;
    }
  }
  
  @media (max-width: 768px) {
    .story-stats {
      grid-template-columns: 1fr;
    }
  
    .team-showcase,
    .values-grid,
    .summary-grid {
      grid-template-columns: 1fr;
    }
  
    .tabs-nav {
      flex-direction: column;
      align-items: center;
    }
  
    .tab-button {
      width: 100%;
      max-width: 30rem;
    }
  }

/* Team Showcase */
.team-showcase {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr)) !important; /* Match original grid */
  gap: 3rem !important; /* Match original gap */
  padding: 4rem 0 !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
  position: relative !important;
  background: var(--primary-bg) !important; /* Use primary background from theme */
}

/* Team Member Card */
.team-member-card {
  background: var(--secondary-bg) !important; /* Use secondary background from theme */
  border-radius: 1rem !important; /* Rounded corners for rectangular tab */
  box-shadow: var(--shadow-md) !important; /* Use shadow from theme */
  transition: transform 0.3s ease, box-shadow 0.3s ease !important; /* Match original hover effect */
  position: relative !important; /* For diagonal positioning */
  padding: 2rem !important; /* Add padding to create space around content */
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  overflow: visible !important; /* Ensure content doesn’t get clipped */
}

.team-member-card:hover {
  transform: translateY(-10px) !important; /* Match original hover effect */
  box-shadow: var(--shadow-lg) !important; /* Use shadow from theme */
}

/* Member Image */
.member-image {
  width: 180px !important; /* Circular image size */
  height: 180px !important; /* Circular image size */
  border-radius: 50% !important; /* Keep image circular */
  overflow: hidden !important;
  margin-bottom: 1.5rem !important; /* Space between image and text */
  border: 4px solid var(--border-color) !important; /* Add a subtle border for contrast */
}

.member-image img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important; /* Ensure image fits the circle */
  object-position: center !important; /* Center the image */
  transform: scale(0.85) !important; /* Zoom out the image slightly to fit better */
  transition: transform 0.3s ease !important; /* Smooth hover effect */
}

.team-member-card:hover .member-image img {
  transform: scale(0.9) !important; /* Slightly zoom in on hover for interactivity */
}

/* Member Info */
.member-info {
  padding: 0 !important; /* Remove extra padding, handled by card */
  text-align: center !important; /* Center align text */
}

.member-info h3 {
  font-size: 2.2rem !important; /* Match original font size */
  margin-bottom: 0.5rem !important;
  color: var(--text-primary) !important; /* Use theme text color */
}

.member-position {
  display: inline-block !important;
  font-size: 1.4rem !important; /* Match original font size */
  color: var(--accent-color) !important; /* Use theme accent color */
  margin-bottom: 1.5rem !important;
}

.member-info p {
  font-size: 1.5rem !important; /* Match original font size */
  margin-bottom: 1.5rem !important;
  color: var(--text-secondary) !important; /* Use theme text color */
  max-width: 80% !important; /* Limit text width for better readability */
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Social Links */
.member-social {
  display: flex !important;
  gap: 1rem !important; /* Match original gap */
}

/* Specific positioning for diagonal effect */
.team-member-card:nth-child(1) { /* CEO - Rizwan Hassan */
  order: 0 !important;
  transform: translateY(0) !important; /* Top position */
}

.team-member-card:nth-child(2) { /* CTO - Moaaz Khokhar */
  order: 1 !important;
  transform: translateY(20px) !important; /* Slightly lower */
}

.team-member-card:nth-child(3) { /* Head of AI Projects - Imran Hassan */
  order: 2 !important;
  transform: translateY(40px) !important; /* Lower than Rizwan and Moaaz */
}

.team-member-card:nth-child(4) { /* Lead GenerativeAI - Mueez ur Rehman */
  order: 3 !important;
  transform: translateY(60px) !important; /* Next row, lower */
}

.team-member-card:nth-child(5) { /* Lead ML Optimization - Talha Tariq */
  order: 4 !important;
  transform: translateY(80px) !important; /* Lowest in next row */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .team-showcase {
    grid-template-columns: 1fr !important; /* Stack on mobile, match original */
  }

  .team-member-card {
    max-width: 100% !important; /* Ensure full width on mobile */
    transform: translateY(0) !important; /* Reset diagonal on mobile */
  }

  .member-image {
    width: 150px !important; /* Slightly smaller on mobile */
    height: 150px !important;
  }
}
