<?php
// Set headers to prevent caching and to specify JSON response
header('Content-Type: application/json');
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

// Check if the form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $phone = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_STRING);
    $service = filter_input(INPUT_POST, 'service', FILTER_SANITIZE_STRING);
    $message = filter_input(INPUT_POST, 'message', FILTER_SANITIZE_STRING);

    // Validate required fields
    if (empty($name) || empty($email) || empty($message)) {
        $response['message'] = 'Please fill in all required fields.';
        echo json_encode($response);
        exit;
    }

    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Please enter a valid email address.';
        echo json_encode($response);
        exit;
    }

    // SMTP configuration
    $sender_email = "<EMAIL>";
    $password = "kniq bxbb lnlu pofm";
    $recipient_email = "<EMAIL>";

    // Email content
    $subject = "New Contact Form Submission from $name";
    
    // Create email body
    $email_body = "
    <html>
    <head>
        <title>New Contact Form Submission</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            h2 { color: #4169e1; border-bottom: 1px solid #eee; padding-bottom: 10px; }
            .field { margin-bottom: 20px; }
            .label { font-weight: bold; }
            .value { margin-top: 5px; }
            .footer { margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <h2>New Contact Form Submission</h2>
            <div class='field'>
                <div class='label'>Name:</div>
                <div class='value'>$name</div>
            </div>
            <div class='field'>
                <div class='label'>Email:</div>
                <div class='value'>$email</div>
            </div>
            <div class='field'>
                <div class='label'>Phone:</div>
                <div class='value'>" . ($phone ? $phone : 'Not provided') . "</div>
            </div>
            <div class='field'>
                <div class='label'>Service:</div>
                <div class='value'>" . ($service ? $service : 'Not selected') . "</div>
            </div>
            <div class='field'>
                <div class='label'>Message:</div>
                <div class='value'>$message</div>
            </div>
            <div class='footer'>
                This message was sent from the Qoverse website contact form.
            </div>
        </div>
    </body>
    </html>
    ";

    // To send HTML mail, the Content-type header must be set
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $sender_email" . "\r\n";
    $headers .= "Reply-To: $email" . "\r\n";

    try {
        // Configure PHP mailer
        require 'PHPMailer/PHPMailer.php';
        require 'PHPMailer/SMTP.php';
        require 'PHPMailer/Exception.php';

        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = 'smtp.gmail.com';
        $mail->SMTPAuth = true;
        $mail->Username = $sender_email;
        $mail->Password = $password;
        $mail->SMTPSecure = 'tls';
        $mail->Port = 587;
        
        // Recipients
        $mail->setFrom($sender_email, 'Qoverse Website');
        $mail->addAddress($recipient_email);
        $mail->addReplyTo($email, $name);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $email_body;
        $mail->AltBody = strip_tags(str_replace(['<div>', '</div>'], ["\n", ''], $email_body));
        
        $mail->send();
        
        $response['success'] = true;
        $response['message'] = 'Thank you for your message! We will get back to you soon.';
    } catch (Exception $e) {
        // Log the error (in a production environment)
        error_log("Mailer Error: " . $e->getMessage());
        
        // Fallback to mail() function if PHPMailer fails
        if (mail($recipient_email, $subject, $email_body, $headers)) {
            $response['success'] = true;
            $response['message'] = 'Thank you for your message! We will get back to you soon.';
        } else {
            $response['message'] = 'Sorry, there was an error sending your message. Please try again later or contact us directly.';
        }
    }
} else {
    $response['message'] = 'Invalid request method.';
}

// Return the response
echo json_encode($response);
?>