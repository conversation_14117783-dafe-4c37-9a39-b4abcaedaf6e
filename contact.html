<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Qoverse - Contact Us</title>
  <link rel="stylesheet" href="styles.css" />
  <link rel="icon" type="image/x-icon" href="assets/favicon.ico" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/tween.js/18.6.4/tween.umd.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/gsap.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/ScrollTrigger.min.js" defer></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <script src="script.js" defer></script>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700&display=swap" rel="stylesheet">
  <style>
    .form-group.error input,
  .form-group.error textarea,
  .form-group.error select {
    border-color: var(--error-color);
    box-shadow: 0 0 0 2px rgba(239, 71, 111, 0.2);
  }

  .form-validation {
    color: var(--error-color);
    font-size: 1.2rem;
    min-height: 1.8rem;
    margin-top: 0.5rem;
  }

  .form-status {
    margin-top: 2rem;
    padding: 1.5rem;
    border-radius: 0.8rem;
    font-size: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    min-height: 0;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
  }

  .form-status.success,
  .form-status.error,
  .form-status.info {
    min-height: 6rem;
    max-height: 10rem;
    opacity: 1;
  }

  .form-status.success {
    background: rgba(15, 224, 106, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(15, 224, 106, 0.2);
  }

  .form-status.error {
    background: rgba(239, 71, 111, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(239, 71, 111, 0.2);
  }
  
  .form-status.info {
    background: rgba(65, 105, 225, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(65, 105, 225, 0.2);
  }
  .form-group.error input,
  .form-group.error textarea,
  .form-group.error select {
    border-color: var(--error-color);
    box-shadow: 0 0 0 2px rgba(239, 71, 111, 0.2);
  }

  .form-validation {
    color: var(--error-color);
    font-size: 1.2rem;
    min-height: 1.8rem;
    margin-top: 0.5rem;
    transition: all 0.3s ease;
  }

  .form-status {
    margin-top: 2rem;
    padding: 1.5rem;
    border-radius: 0.8rem;
    font-size: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    min-height: 0;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
  }

  .form-status.success,
  .form-status.error {
    min-height: 6rem;
    max-height: 10rem;
    opacity: 1;
  }

  .form-status.success {
    background: rgba(15, 224, 106, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(15, 224, 106, 0.2);
  }

  .form-status.error {
    background: rgba(239, 71, 111, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(239, 71, 111, 0.2);
  }

  /* Improve form loading state */
  .form-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  /* Add focus styles for better accessibility */
  .form-group input:focus,
  .form-group textarea:focus,
  .form-group select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.2);
  }

    /* Add these styles to styles.css or inline in contact.html */
.form-group.error input,
.form-group.error textarea,
.form-group.error select {
  border-color: var(--error-color);
  box-shadow: 0 0 0 2px rgba(239, 71, 111, 0.2);
}

.form-validation {
  color: var(--error-color);
  font-size: 1.2rem;
  min-height: 1.8rem;
  margin-top: 0.5rem;
  transition: all 0.3s ease;
}

.form-status {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 0.8rem;
  font-size: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  min-height: 0;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
}

.form-status.success,
.form-status.error {
  min-height: 6rem;
  max-height: 10rem;
  opacity: 1;
}

.form-status.success {
  background: rgba(15, 224, 106, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(15, 224, 106, 0.2);
}

.form-status.error {
  background: rgba(239, 71, 111, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 71, 111, 0.2);
}

/* Improve form loading state */
.form-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Add focus styles for better accessibility */
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.2);
}
    /* Enhanced Dropdown Menu Styles */
    .dropdown-wrapper {
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      min-width: 28rem;
      background: rgba(26, 26, 42, 0.95); /* Increased opacity for less transparency */
      border-radius: 1.5rem; /* Slightly larger radius for a softer look */
      padding: 2rem; /* Increased padding for a more spacious feel */
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4); /* Deeper shadow for a premium effect */
      opacity: 0;
      visibility: hidden;
      transform-origin: top center;
      transform: translateX(-50%) translateY(1rem);
      transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1); /* Smoother easing */
      z-index: 100;
      backdrop-filter: blur(15px); /* Enhanced glassmorphism effect */
      border: 1px solid rgba(65, 105, 225, 0.3); /* Softer border color */
      overflow: hidden; /* Prevent overflow issues */
    }
    
    .dropdown:hover .dropdown-wrapper {
      opacity: 1;
      visibility: visible;
      transform: translateX(-50%) translateY(0);
    }
    
    .dropdown-content {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1.2rem; /* Slightly increased gap for elegance */
      max-height: 65vh;
      overflow-y: auto;
      padding-right: 0.6rem;
    }
    
    .dropdown-content::-webkit-scrollbar {
      width: 6px; /* Slightly thicker scrollbar */
    }
    
    .dropdown-content::-webkit-scrollbar-thumb {
      background: linear-gradient(45deg, #4169e1, #8a2be2); /* Gradient for a sexy touch */
      border-radius: 6px;
      transition: background 0.3s ease; /* Smooth transition on hover */
    }
    
    .dropdown-content::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(45deg, #8a2be2, #ff69b4); /* Vibrant hover effect */
    }
    
    .dropdown-link {
      font-size: 1.5rem; /* Slightly larger text for readability */
      padding: 1.4rem 1.8rem; /* More padding for a luxurious feel */
      border-radius: 1rem; /* Larger radius for a modern curve */
      display: flex;
      align-items: center;
      transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1); /* Smoother transition */
      border-left: 4px solid transparent; /* Thicker border for emphasis */
      background: linear-gradient(135deg, rgba(65, 105, 225, 0.1), rgba(138, 43, 226, 0.1)); /* Subtle gradient background */
      color: #e6e6fa; /* Light purple text for elegance */
      text-shadow: 0 0 5px rgba(138, 43, 226, 0.3); /* Soft glow effect */
    }
    
    .dropdown-link span {
      display: inline-block;
      margin-right: 1.5rem; /* Increased spacing */
      color: #ff69b4; /* Hot pink for a sexy accent */
      font-weight: 700; /* Bolder number */
      font-family: 'Montserrat', sans-serif; /* Modern font suggestion */
      opacity: 1; /* Fully opaque for contrast */
      min-width: 3rem; /* Wider number box */
      text-align: center;
      background: rgba(255, 105, 180, 0.2); /* Subtle background for numbers */
      border-radius: 50%; /* Circular number badge */
      padding: 0.5rem;
    }
    
    .dropdown-link:hover {
      background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(255, 105, 180, 0.2)); /* Gradient hover effect */
      color: #fff; /* White text on hover */
      border-left-color: #ff69b4; /* Hot pink border on hover */
      transform: translateX(8px) scale(1.05); /* Enhanced hover animation */
      box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3); /* Glowy shadow */
    }

    /* Interactive Map Styles */
    .contact-map {
      position: relative;
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: var(--shadow-lg);
      height: 32rem;
    }
    
    .map-iframe {
      width: 100%;
      height: 100%;
      border: none;
      filter: grayscale(0.7) contrast(1.2);
      transition: all 0.5s ease;
    }
    
    .contact-map:hover .map-iframe {
      filter: grayscale(0) contrast(1);
    }
    
    .map-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(65, 105, 225, 0.2), transparent);
      pointer-events: none;
      z-index: 1;
    }
    
    .map-pin {
      position: absolute;
      bottom: 1.5rem;
      right: 1.5rem;
      background: var(--accent-color);
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 2rem;
      font-size: 1.4rem;
      font-weight: 600;
      z-index: 2;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 0.8rem;
      transition: transform 0.3s ease;
    }
    
    .map-pin:hover {
      transform: translateY(-5px);
    }
    
    .map-pin i {
      font-size: 1.6rem;
    }

    /* Enhanced FAQ Accordion */
    .faq-item {
      background: var(--secondary-bg);
      border-radius: 1rem;
      margin-bottom: 1.5rem;
      overflow: hidden;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      cursor: pointer;
    }
    
    .faq-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }
    
    .faq-question {
      padding: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .faq-question h3 {
      font-size: 1.8rem;
      margin-bottom: 0;
      transition: color 0.3s ease;
    }
    
    .faq-item:hover .faq-question h3 {
      color: var(--accent-color);
    }
    
    .question-icon {
      width: 3rem;
      height: 3rem;
      background: rgba(65, 105, 225, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.4rem;
      color: var(--accent-color);
      transition: all 0.3s ease;
      flex-shrink: 0;
      margin-left: 1.5rem;
    }
    
    .faq-item.active .question-icon {
      transform: rotate(45deg);
      background: var(--accent-color);
      color: white;
    }
    
    .faq-answer {
      max-height: 0;
      overflow: hidden;
      padding: 0 2rem;
      transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
    }
    
    .faq-item.active .faq-answer {
      max-height: 500px;
      padding: 0 2rem 2rem;
    }
    
    .faq-answer p {
      margin: 0;
      font-size: 1.5rem;
      line-height: 1.7;
    }
  </style>
</head>
<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="loader">
      <svg viewBox="0 0 80 80">
        <circle id="loader-circle" cx="40" cy="40" r="32"></circle>
      </svg>
      <div class="loader-text">QOVERSE</div>
    </div>
  </div>

  <!-- Cursor -->
  <div class="cursor-dot"></div>
  <div class="cursor-outline"></div>
  
  <header>
    <div class="header-container">
      <div class="logo-container">
        <img src="assets/logo.png" alt="Qoverse Logo" class="logo-img">
      </div>
      <div class="nav-toggle">
        <div class="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      <nav>
        <ul class="nav-links">
          <li><a href="/" class="nav-link">Home</a></li>
          <li class="dropdown">
            <a href="/services" class="nav-link">Services</a>
            <div class="dropdown-wrapper">
              <ul class="dropdown-content">
                <li><a href="/services#generative-ai" class="dropdown-link"><span>01</span> Generative AI</a></li>
                <li><a href="/services#web-mobile" class="dropdown-link"><span>02</span> Web & Mobile Development</a></li>
                <li><a href="/services#data-management" class="dropdown-link"><span>03</span> Data & Analytics</a></li>
                <li><a href="/services#digital-commerce" class="dropdown-link"><span>04</span> Digital Commerce</a></li>
                <li><a href="/services#ai-bi" class="dropdown-link"><span>05</span> AI Business Intelligence</a></li>
                <li><a href="/services#cloud-native" class="dropdown-link"><span>06</span> Cloud-Native Solutions</a></li>
                <li><a href="/services#product-design" class="dropdown-link"><span>07</span> Product Design & UI/UX</a></li>
                <li><a href="/services#custom-software" class="dropdown-link"><span>08</span> Custom Software</a></li>
                <li><a href="/services#devops" class="dropdown-link"><span>09</span> DevOps & Automation</a></li>
                <li><a href="/services#consulting" class="dropdown-link"><span>10</span> Technology Consulting</a></li>
              </ul>
            </div>
          </li>
          <li><a href="/about" class="nav-link">About Us</a></li>
          <li><a href="/contact" class="nav-link active">Contact</a></li>
        </ul>
      </nav>
      <div class="theme-toggle">
        <span class="theme-icon light-icon">☀️</span>
        <span class="theme-icon dark-icon active">🌙</span>
      </div>
    </div>
  </header>

  <main>
    <section id="hero">
      <div class="parallax"></div>
      <div id="three-canvas"></div>
      <div class="hero-content container">
        <div class="hero-text">
          <h1 class="glitch-effect" data-text="Contact Us">Contact Us</h1>
          <p class="typewriter">Connect with our team to turn your vision into reality.</p>
        </div>
        <div class="scroll-indicator">
          <div class="mouse">
            <div class="mouse-wheel"></div>
          </div>
          <div class="scroll-text">Scroll Down</div>
        </div>
      </div>
    </section>

     <section id="contact-main" class="section">
      <div class="section-pattern"></div>
      <div class="container">
        <div class="section-header">
          <span class="section-subtitle">Get In Touch</span>
          <h2 class="section-title">Let's Start a Conversation</h2>
          <p class="section-description">We're here to answer your questions and discuss how we can help with your technology needs.</p>
        </div>
        <div class="contact-grid">
          <div class="contact-details">
            <div class="contact-map">
              <iframe 
                class="map-iframe"
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3094.3822098516036!2d-75.52553542432881!3d39.14765877136802!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c764a9e3ced00d%3A0x2d4c7db26399f4b3!2s1111%20S%20Governors%20Ave%20%2326893%2C%20Dover%2C%20DE%2019904%2C%20USA!5e0!3m2!1sen!2sin!4v1709941242192!5m2!1sen!2sin" 
                allowfullscreen="" 
                loading="lazy" 
                referrerpolicy="no-referrer-when-downgrade">
              </iframe>
              <div class="map-overlay"></div>
              <div class="map-pin">
                <i class="fas fa-map-marker-alt"></i>
                <span>Our Office</span>
              </div>
            </div>
            <div class="contact-info-cards">
              <div class="info-card">
                <div class="info-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="info-content">
                  <h3>Our Location</h3>
                  <p>1111b S Governors Ave STE 26893<br>1111b S Governors Ave STE 26893 Dover, DE, 19904 US</p>
                </div>
              </div>
              <div class="info-card">
                <div class="info-icon">
                  <i class="fas fa-phone-alt"></i>
                </div>
                <div class="info-content">
                  <h3>Phone</h3>
                  <p><a href="tel:+***********">+****************</a></p>
                </div>
              </div>
              <div class="info-card">
                <div class="info-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <div class="info-content">
                  <h3>Email</h3>
                  <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
              </div>
              <div class="info-card">
                <div class="info-icon">
                  <i class="fas fa-globe"></i>
                </div>
                <div class="info-content">
                  <h3>Website</h3>
                  <p><a href="https://www.qoverse.com" target="_blank">www.qoverse.com</a></p>
                </div>
              </div>
            </div>
            <div class="company-info">
              <h3>About Qoverse</h3>
              <p><strong>Industry:</strong> Software Development</p>
              <p><strong>Specialties:</strong> AI Solutions, Machine Learning Optimization, Web Development, Mathematical Modeling, Data Analytics, Generative AI, Custom SaaS, Automation, Predictive Analytics, Cloud Solutions, UI/UX Design</p>
              <div class="social-links">
                <a href="https://www.linkedin.com/company/qoverse" target="_blank" class="social-link">
                  <i class="fab fa-linkedin"></i>f
                </a>
                <a href="https://www.x.com/qoverse" target="_blank" class="social-link">
                  <i class="fab fa-twitter"></i>
                </a>
<!--                 <a href="https://www.github.com/qoverse" target="_blank" class="social-link">
                  <i class="fab fa-github"></i> -->
                </a>
              </div>
            </div>
          </div>
          <!-- This is the updated contact form section to replace in contact.html -->
          <div class="contact-form-container">
            <form id="contact-form" class="contact-form" action="javascript:void(0);" method="POST">
              <div class="form-header">
                <h3>Send Us a Message</h3>
                <p>Fill out the form below and we'll get back to you as soon as possible.</p>
              </div>
              <div class="form-group">
                <input type="text" id="name" name="name" placeholder="Your Name" required />
                <div class="form-validation"></div>
              </div>
              <div class="form-group">
                <input type="email" id="email" name="email" placeholder="Your Email" required />
                <div class="form-validation"></div>
              </div>
              <div class="form-group">
                <input type="tel" id="phone" name="phone" placeholder="Your Phone" />
                <div class="form-validation"></div>
              </div>
              <div class="form-group">
                <select id="service" name="service">
                  <option value="" disabled selected>Select Service</option>
                  <option value="Generative AI">Generative AI</option>
                  <option value="Web & Mobile Development">Web & Mobile Development</option>
                  <option value="Data & Analytics">Data & Analytics</option>
                  <option value="Digital Commerce">Digital Commerce</option>
                  <option value="AI Business Intelligence">AI Business Intelligence</option>
                  <option value="Cloud-Native Solutions">Cloud-Native Solutions</option>
                  <option value="Product Design & UI/UX">Product Design & UI/UX</option>
                  <option value="Custom Software">Custom Software</option>
                  <option value="DevOps & Automation">DevOps & Automation</option>
                  <option value="Technology Consulting">Technology Consulting</option>
                  <option value="Other">Other</option>
                </select>
                <div class="form-validation"></div>
              </div>
              <div class="form-group">
                <textarea id="message" name="message" placeholder="Your Message" required></textarea>
                <div class="form-validation"></div>
              </div>
              <button type="submit" class="cta-button primary form-submit">
                <span class="button-text">Send Message</span>
                <span class="button-icon">→</span>
              </button>
              <div class="form-status"></div>
            </form>
          </div>
        </section> 
          <!-- <div class="contact-form-container">
            <form id="contact-form" class="contact-form">
              <div class="form-header">
                <h3>Send Us a Message</h3>
                <p>Fill out the form below and we'll get back to you as soon as possible.</p>
              </div>
              <div class="form-group">
                <input type="text" id="name" name="name" placeholder="Your Name" required />
                <div class="form-validation"></div>
              </div>
              <div class="form-group">
                <input type="email" id="email" name="email" placeholder="Your Email" required />
                <div class="form-validation"></div>
              </div>
              <div class="form-group">
                <input type="tel" id="phone" name="phone" placeholder="Your Phone" />
                <div class="form-validation"></div>
              </div>
              <div class="form-group">
                <select id="service" name="service">
                  <option value="" disabled selected>Select Service</option>
                  <option value="ai">Generative AI</option>
                  <option value="web">Web & Mobile Development</option>
                  <option value="data">Data & Analytics</option>
                  <option value="commerce">Digital Commerce</option>
                  <option value="business">AI Business Intelligence</option>
                  <option value="cloud">Cloud-Native Solutions</option>
                  <option value="design">Product Design & UI/UX</option>
                  <option value="custom">Custom Software</option>
                  <option value="devops">DevOps & Automation</option>
                  <option value="consulting">Technology Consulting</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div class="form-group">
                <textarea id="message" name="message" placeholder="Your Message" required></textarea>
                <div class="form-validation"></div>
              </div>
              <button type="submit" class="cta-button primary form-submit">
                <span class="button-text">Send Message</span>
                <span class="button-icon">→</span>
              </button>
              <div class="form-status"></div>
            </form>
          </div>
        </div>
      </div>
    </section> -->

    <section id="faq" class="section">
      <div class="container">
        <div class="section-header">
          <span class="section-subtitle">Questions & Answers</span>
          <h2 class="section-title">Frequently Asked Questions</h2>
          <p class="section-description">Find answers to common questions about our services and process.</p>
        </div>
        <div class="faq-container">
          <div class="faq-item">
            <div class="faq-question">
              <h3>What types of businesses do you work with?</h3>
              <div class="question-icon">
                <i class="fas fa-plus"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>We work with businesses of all sizes, from startups to enterprise organizations, across various industries including healthcare, finance, e-commerce, education, manufacturing, and more. Our solutions are tailored to meet the specific needs of each client.</p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>How long does a typical project take to complete?</h3>
              <div class="question-icon">
                <i class="fas fa-plus"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>Project timelines vary depending on scope, complexity, and requirements. Small projects may take 2-4 weeks, while more complex enterprise solutions can take several months. During our initial consultation, we'll provide a detailed timeline based on your specific project needs.</p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>What is your development process?</h3>
              <div class="question-icon">
                <i class="fas fa-plus"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>We follow an agile development methodology that includes: 1) Discovery and planning, 2) Design and prototyping, 3) Development with regular iterations, 4) Testing and quality assurance, 5) Deployment, and 6) Ongoing support and maintenance. This approach ensures transparency, flexibility, and quality throughout the project lifecycle.</p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>How do you handle project management and communication?</h3>
              <div class="question-icon">
                <i class="fas fa-plus"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>We assign a dedicated project manager to each client who serves as your main point of contact. We use collaboration tools like Jira, Slack, and regular video meetings to ensure clear communication. You'll receive regular updates on progress, milestones, and any challenges that arise during development.</p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>Do you provide ongoing support after project completion?</h3>
              <div class="question-icon">
                <i class="fas fa-plus"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>Yes, we offer various support and maintenance packages to ensure your solution continues to perform optimally. These include technical support, bug fixes, security updates, feature enhancements, and performance optimization. We can tailor a support plan to meet your specific needs and budget.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer>
    <div class="footer-main">
      <div class="container">
        <div class="footer-grid">
          <div class="footer-info">
            <img src="assets/logo.png" alt="Qoverse Logo" class="footer-logo" />
            <p class="footer-tagline">Creating innovative software solutions that power digital transformation.</p>
            <div class="footer-social">
              <a href="https://www.linkedin.com/company/qoverse" target="_blank" class="social-link">
                <i class="fab fa-linkedin"></i>
              </a>
              <a href="https://www.x.com/qoverse" target="_blank" class="social-link">
                <i class="fab fa-twitter"></i>
              </a>
<!--               <a href="https://www.github.com/qoverse" target="_blank" class="social-link">
                <i class="fab fa-github"></i> -->
              </a>
            </div>
          </div>
          <div class="footer-nav">
            <h4>Quick Links</h4>
            <ul>
              <li><a href="/">Home</a></li>
              <li><a href="/services">Services</a></li>
              <li><a href="/about">About Us</a></li>
              <li><a href="/contact">Contact</a></li>
              <!-- <li><a href="careers.html">Careers</a></li> -->
              <!-- <li><a href="blog.html">Blog</a></li> -->
            </ul>
          </div>
          <div class="footer-services">
            <h4>Our Services</h4>
            <ul>
              <li><a href="/services#generative-ai">Generative AI</a></li>
              <li><a href="/services#web-mobile">Web & Mobile Development</a></li>
              <li><a href="/services#data-management">Data & Analytics</a></li>
              <li><a href="/services#digital-commerce">Digital Commerce</a></li>
              <li><a href="/services#ai-bi">AI Business Intelligence</a></li>
              <li><a href="/services#cloud-native">Cloud-Native Solutions</a></li>
            </ul>
          </div>
          <div class="footer-contact">
            <h4>Contact Us</h4>
            <div class="contact-item">
              <i class="fas fa-map-marker-alt"></i>
              <p>1111b S Governors Ave STE 26893<br>1111b S Governors Ave STE 26893 Dover, DE, 19904 US</p>
            </div>
            <div class="contact-item">
              <i class="fas fa-phone-alt"></i>
              <p><a href="tel:+***********">+****************</a></p>
            </div>
            <div class="contact-item">
              <i class="fas fa-envelope"></i>
              <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <div class="container">
        <p>&copy; 2025 Qoverse. All rights reserved.</p>
        <div class="footer-links">
          <!-- <a href="privacy.html">Privacy Policy</a> -->
          <!-- <a href="terms.html">Terms of Service</a> -->
        </div>
      </div>
    </div>
  </footer>

  <!-- Sound Toggle -->
  <div class="sound-toggle">
    <div class="sound-icon sound-on active">
      <i class="fas fa-volume-up"></i>
    </div>
    <div class="sound-icon sound-off">
      <i class="fas fa-volume-mute"></i>
    </div>
  </div>

  <!-- Back to Top Button -->
  <div class="back-to-top">
    <i class="fas fa-chevron-up"></i>
  </div>

  <!-- Audio Elements -->
  <audio id="background-sound" loop>
    <source src="assets/cosmos.mp3" type="audio/mpeg" />
  </audio>
  <audio id="hover-sound">
    <source src="assets/hover.mp3" type="audio/mpeg" />
  </audio>
  <audio id="click-sound">
    <source src="assets/click.mp3" type="audio/mpeg" />
  </audio>

 <!-- Replace the entire script block at the bottom of contact.html -->
<script>
  // This is a completely standalone script that handles both the FAQ and form
  document.addEventListener('DOMContentLoaded', function() {
    // FAQ Accordion functionality
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
      const question = item.querySelector('.faq-question');
      
      question.addEventListener('click', () => {
        // Close all other items
        faqItems.forEach(otherItem => {
          if (otherItem !== item && otherItem.classList.contains('active')) {
            otherItem.classList.remove('active');
          }
        });
        
        // Toggle current item
        item.classList.toggle('active');
        
        // Play sound effect if enabled
        const clickSound = document.getElementById('click-sound');
        if (clickSound && window.audioEnabled) {
          clickSound.currentTime = 0;
          clickSound.play().catch(e => {});
        }
      });
    });
    
    // Make first FAQ item active by default
    if (faqItems.length > 0) {
      setTimeout(() => {
        faqItems[0].classList.add('active');
      }, 1000);
    }
  });
// Wait for the entire page to finish loading (including all scripts)
window.addEventListener('load', function() {
  console.log('Window loaded - Form handler initializing');
  
  // Find the contact form element
  const contactForm = document.getElementById('contact-form');
  
  if (contactForm) {
    console.log('Form found, attaching handler');
    
    // Override any existing handlers and ensure this is the one that runs
    contactForm.onsubmit = async function(event) {
      // Prevent default form submission and any other handlers
      event.preventDefault();
      event.stopPropagation();
      console.log('Form submit button clicked');
      
      // Find form elements
      const formStatus = contactForm.querySelector('.form-status');
      const submitButton = contactForm.querySelector('.form-submit');
      
      // Basic validation
      let isValid = true;
      const requiredFields = contactForm.querySelectorAll('[required]');
      
      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          isValid = false;
          const validationDiv = field.parentElement.querySelector('.form-validation');
          if (validationDiv) {
            validationDiv.textContent = 'This field is required';
            field.parentElement.classList.add('error');
          }
        } else if (field.type === 'email') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(field.value)) {
            isValid = false;
            const validationDiv = field.parentElement.querySelector('.form-validation');
            if (validationDiv) {
              validationDiv.textContent = 'Please enter a valid email address';
              field.parentElement.classList.add('error');
            }
          }
        }
      });
      
      if (!isValid) {
        console.log('Form validation failed');
        return false;
      }
      
      // Show sending state
      submitButton.disabled = true;
      submitButton.innerHTML = '<span class="button-text">Sending...</span>';
      
      if (formStatus) {
        formStatus.textContent = 'Sending your message...';
        formStatus.style.opacity = '1';
        formStatus.classList.remove('success', 'error');
        formStatus.classList.add('info');
      }
      
      // Collect form data
      const formData = {
        name: contactForm.querySelector('#name').value,
        email: contactForm.querySelector('#email').value,
        phone: contactForm.querySelector('#phone').value,
        service: contactForm.querySelector('#service').value,
        message: contactForm.querySelector('#message').value
      };
      
      console.log('Sending data:', formData);
      
      // Send the API request
      try {
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        });
        
        console.log('Response status:', response.status);
        
        // Parse the response
        const result = await response.json();
        console.log('Response data:', result);
        
        // Handle success or error
        if (response.ok) {
          if (formStatus) {
            formStatus.textContent = result.message || 'Message sent successfully!';
            formStatus.classList.remove('info', 'error');
            formStatus.classList.add('success');
          }
          
          // Reset the form
          contactForm.reset();
        } else {
          if (formStatus) {
            formStatus.textContent = result.error || 'Failed to send message. Please try again.';
            formStatus.classList.remove('info', 'success');
            formStatus.classList.add('error');
          }
        }
      } catch (error) {
        console.error('Error submitting form:', error);
        
        if (formStatus) {
          formStatus.textContent = 'Network error. Please try again later.';
          formStatus.classList.remove('info', 'success');
          formStatus.classList.add('error');
        }
      } finally {
        // Restore button state
        submitButton.disabled = false;
        submitButton.innerHTML = '<span class="button-text">Send Message</span><span class="button-icon">→</span>';
      }
      
      // Prevent default form submission
      return false;
    };
    
    // Add real-time validation
    const formInputs = contactForm.querySelectorAll('input, textarea, select');
    formInputs.forEach(input => {
      input.addEventListener('input', function() {
        const validationDiv = this.parentElement.querySelector('.form-validation');
        if (validationDiv && validationDiv.textContent) {
          validationDiv.textContent = '';
          this.parentElement.classList.remove('error');
        }
      });
    });
    
    console.log('Form handler successfully installed');
  } else {
    console.error('Contact form not found');
  }
});
</script>
<script src="contact-form.js"></script>
</body>
</html>
