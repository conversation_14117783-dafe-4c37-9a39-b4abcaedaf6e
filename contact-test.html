<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact Form Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    
    h1 {
      margin-bottom: 30px;
      color: #4169e1;
    }
    
    .form-container {
      background: #f5f5f5;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    input,
    textarea,
    select {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }
    
    textarea {
      min-height: 100px;
    }
    
    button {
      background: #4169e1;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    
    button:hover {
      background: #3050c0;
    }
    
    button:disabled {
      background: #b0b0b0;
      cursor: not-allowed;
    }
    
    .form-status {
      margin-top: 20px;
      padding: 15px;
      border-radius: 4px;
      text-align: center;
    }
    
    .success {
      background: #e0ffe0;
      color: #008000;
      border: 1px solid #00800050;
    }
    
    .error {
      background: #ffe0e0;
      color: #800000;
      border: 1px solid #80000050;
    }
    
    .info {
      background: #e0e0ff;
      color: #000080;
      border: 1px solid #00008050;
    }
    
    .test-controls {
      margin-top: 30px;
      border-top: 1px solid #ddd;
      padding-top: 20px;
    }
    
    .test-button {
      background: #ffa500;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    
    .test-results {
      background: #f0f0f0;
      padding: 15px;
      border-radius: 4px;
      margin-top: 20px;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 300px;
      overflow: auto;
    }
  </style>
</head>
<body>
  <h1>Contact Form Test Page</h1>
  
  <div class="form-container">
    <form id="contact-test-form">
      <div class="form-group">
        <label for="name">Name*</label>
        <input type="text" id="name" name="name" required>
      </div>
      
      <div class="form-group">
        <label for="email">Email*</label>
        <input type="email" id="email" name="email" required>
      </div>
      
      <div class="form-group">
        <label for="phone">Phone</label>
        <input type="tel" id="phone" name="phone">
      </div>
      
      <div class="form-group">
        <label for="service">Service</label>
        <select id="service" name="service">
          <option value="" selected>Select a service</option>
          <option value="Generative AI">Generative AI</option>
          <option value="Web & Mobile Development">Web & Mobile Development</option>
          <option value="Data & Analytics">Data & Analytics</option>
          <option value="Other">Other</option>
        </select>
      </div>
      
      <div class="form-group">
        <label for="message">Message*</label>
        <textarea id="message" name="message" required></textarea>
      </div>
      
      <button type="submit" id="submit-button">Send Message</button>
      
      <div class="form-status" id="form-status" style="display: none;"></div>
    </form>
    
    <div class="test-controls">
      <h3>API Tests</h3>
      <button class="test-button" id="test-api">Test API Connection</button>
      <button class="test-button" id="test-env">Check Environment Variables</button>
      <div class="test-results" id="test-results"></div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('contact-test-form');
      const submitButton = document.getElementById('submit-button');
      const formStatus = document.getElementById('form-status');
      const testApiButton = document.getElementById('test-api');
      const testEnvButton = document.getElementById('test-env');
      const testResults = document.getElementById('test-results');
      
      // Base API URL - change this to your domain
      const apiBaseUrl = window.location.origin;
      
      // Form submission
      form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Show loading state
        submitButton.disabled = true;
        submitButton.textContent = 'Sending...';
        formStatus.textContent = 'Sending your message...';
        formStatus.className = 'form-status info';
        formStatus.style.display = 'block';
        
        try {
          // Collect form data
          const formData = {
            name: document.getElementById('name').value,
            email: document.getElementById('email').value,
            phone: document.getElementById('phone').value,
            service: document.getElementById('service').value,
            message: document.getElementById('message').value
          };
          
          // Send request
          const response = await fetch(`${apiBaseUrl}/api/contact`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
          });
          
          const result = await response.json();
          
          if (response.ok) {
            formStatus.textContent = result.message || 'Message sent successfully!';
            formStatus.className = 'form-status success';
            form.reset();
          } else {
            formStatus.textContent = result.error || 'Failed to send message';
            formStatus.className = 'form-status error';
            
            if (result.debug) {
              const debugInfo = document.createElement('div');
              debugInfo.style.fontSize = '14px';
              debugInfo.style.marginTop = '10px';
              debugInfo.style.textAlign = 'left';
              debugInfo.textContent = `Debug: ${result.debug}`;
              formStatus.appendChild(debugInfo);
            }
          }
        } catch (error) {
          formStatus.textContent = `Network error: ${error.message}`;
          formStatus.className = 'form-status error';
        } finally {
          submitButton.disabled = false;
          submitButton.textContent = 'Send Message';
        }
      });
      
      // Test API connection
      testApiButton.addEventListener('click', async function() {
        testResults.textContent = 'Testing API connection...';
        
        try {
          const response = await fetch(`${apiBaseUrl}/api/test`);
          const data = await response.json();
          testResults.textContent = `API test successful:\n${JSON.stringify(data, null, 2)}`;
        } catch (error) {
          testResults.textContent = `API test failed: ${error.message}`;
        }
      });
      
      // Test environment variables
      testEnvButton.addEventListener('click', async function() {
        testResults.textContent = 'Checking environment variables...';
        
        try {
          const response = await fetch(`${apiBaseUrl}/api/test`);
          const data = await response.json();
          
          if (data.environment) {
            testResults.textContent = `Environment variables:\n${JSON.stringify(data.environment, null, 2)}`;
            
            // Add warnings if environment variables are missing
            const warnings = [];
            
            if (!data.environment.SMTP_USER_SET) {
              warnings.push('⚠️ SMTP_USER is not set!');
            }
            
            if (!data.environment.SMTP_PASS_SET) {
              warnings.push('⚠️ SMTP_PASS is not set!');
            }
            
            if (!data.environment.RECIPIENT_EMAIL_SET) {
              warnings.push('⚠️ RECIPIENT_EMAIL is not set!');
            }
            
            if (warnings.length > 0) {
              testResults.textContent += `\n\n${warnings.join('\n')}`;
            }
          } else {
            testResults.textContent = `Could not get environment data:\n${JSON.stringify(data, null, 2)}`;
          }
        } catch (error) {
          testResults.textContent = `Environment check failed: ${error.message}`;
        }
      });
    });
  </script>
</body>
</html>