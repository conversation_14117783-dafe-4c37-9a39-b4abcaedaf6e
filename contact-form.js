// contact-form.js
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    
    if (contactForm) {
      console.log('Contact form handler initialized');
      
      // Override the form submission handler
      contactForm.addEventListener('submit', async function(event) {
        // CRITICAL: Prevent the default form submission which is causing the 404
        event.preventDefault();
        
        // Find UI elements
        const formStatus = contactForm.querySelector('.form-status');
        const submitButton = contactForm.querySelector('.form-submit');
        
        // Basic validation
        let isValid = true;
        const requiredFields = contactForm.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
          if (!field.value.trim()) {
            isValid = false;
            const validationDiv = field.parentElement.querySelector('.form-validation');
            if (validationDiv) {
              validationDiv.textContent = 'This field is required';
              field.parentElement.classList.add('error');
            }
          } else if (field.type === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(field.value)) {
              isValid = false;
              const validationDiv = field.parentElement.querySelector('.form-validation');
              if (validationDiv) {
                validationDiv.textContent = 'Please enter a valid email address';
                field.parentElement.classList.add('error');
              }
            }
          }
        });
        
        if (!isValid) {
          console.log('Form validation failed');
          return false;
        }
        
        // Show sending state
        if (submitButton) {
          submitButton.disabled = true;
          const buttonText = submitButton.querySelector('.button-text');
          if (buttonText) buttonText.textContent = 'Sending...';
        }
        
        if (formStatus) {
          formStatus.textContent = 'Sending your message...';
          formStatus.style.opacity = '1';
          formStatus.classList.remove('success', 'error');
          formStatus.classList.add('info');
        }
        
        // Collect form data
        const formData = {
          name: contactForm.querySelector('#name').value,
          email: contactForm.querySelector('#email').value,
          phone: contactForm.querySelector('#phone').value || 'Not provided',
          service: contactForm.querySelector('#service').value || 'Not specified',
          message: contactForm.querySelector('#message').value
        };
        
        console.log('Sending data:', formData);
        
        try {
          // Use the API route - CRITICAL: this must be the correct path to your API
          const response = await fetch('/api/contact', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
          });
          
          console.log('Response status:', response.status);
          
          // Parse the response
          const result = await response.json();
          console.log('API response:', result);
          
          // Handle success or error
          if (response.ok) {
            if (formStatus) {
              formStatus.textContent = result.message || 'Your message has been sent successfully!';
              formStatus.classList.remove('info', 'error');
              formStatus.classList.add('success');
            }
            
            // Reset the form
            contactForm.reset();
          } else {
            if (formStatus) {
              formStatus.textContent = result.error || 'Failed to send message. Please try again.';
              formStatus.classList.remove('info', 'success');
              formStatus.classList.add('error');
            }
          }
        } catch (error) {
          console.error('Error submitting form:', error);
          
          if (formStatus) {
            formStatus.textContent = 'Network error. Please email us <NAME_EMAIL>';
            formStatus.classList.remove('info', 'success');
            formStatus.classList.add('error');
          }
        } finally {
          // Restore button state
          if (submitButton) {
            submitButton.disabled = false;
            const buttonText = submitButton.querySelector('.button-text');
            if (buttonText) buttonText.textContent = 'Send Message';
          }
        }
      });
      
      // Add real-time validation
      const formInputs = contactForm.querySelectorAll('input, textarea, select');
      formInputs.forEach(input => {
        input.addEventListener('input', function() {
          const validationDiv = this.parentElement.querySelector('.form-validation');
          if (validationDiv && validationDiv.textContent) {
            validationDiv.textContent = '';
            this.parentElement.classList.remove('error');
          }
        });
      });
    }
  });