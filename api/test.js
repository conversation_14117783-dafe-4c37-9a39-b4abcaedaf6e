// api/test.js
export default function handler(req, res) {
    // Add CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    // Check environment variables
    const envCheck = {
      SMTP_USER_SET: !!process.env.SMTP_USER,
      SMTP_PASS_SET: !!process.env.SMTP_PASS,
      RECIPIENT_EMAIL_SET: !!process.env.RECIPIENT_EMAIL,
      NODE_ENV: process.env.NODE_ENV,
      VERCEL_ENV: process.env.VERCEL_ENV
    };
    
    return res.status(200).json({
      message: "API endpoint is working correctly",
      timestamp: new Date().toISOString(),
      method: req.method,
      environment: envCheck
    });
  }